use ndarray::Array2;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Prediction {
    label: String,
    output: Array2<f64>,
    confidence: Confidence,
}

impl Prediction {
    pub fn new(label: &str, output: Array2<f64>, score: f64) -> Self {
        Self {
            label: label.to_string(),
            output,
            confidence: Confidence::new(score),
        }
    }

    pub fn get_confidence(&self) -> Confidence {
        self.confidence
    }

    pub fn get_output(&self) -> Array2<f64> {
        self.output.clone()
    }

    pub fn get_label(&self) -> String {
        self.label.clone()
    }
}

/// A utility class for comparing confidence levels in code.
/// IE:
/// ```ignore
/// let prediction = network.predict(&data);
///
/// if prediction.confidence_level == Confidence::Certain {}
/// ```
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>)]
pub enum ConfidenceLevel {
    /// >= 0.995
    Certain,
    /// >= 0.97 and < 0.995
    High,
    /// >= 0.90 and < 0.97
    Medium,
    /// >= 0.75 and < 0.90
    Low,
    /// < 0.75
    SuperLow,
}

impl ConfidenceLevel {
    pub fn from_score(score: f64) -> Self {
        match score {
            x if x >= 0.995 => Self::Certain,
            x if x >= 0.97 => Self::High,
            x if x >= 0.90 => Self::Medium,
            x if x >= 0.75 => Self::Low,
            _ => Self::SuperLow,
        }
    }
}

impl std::fmt::Display for ConfidenceLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Certain => write!(f, "Certain"),
            Self::High => write!(f, "High"),
            Self::Medium => write!(f, "Medium"),
            Self::Low => write!(f, "Low"),
            Self::SuperLow => write!(f, "Super Low"),
        }
    }
}

#[derive(Debug, Clone, Copy)]
pub struct Confidence {
    score: f64,
    level: ConfidenceLevel,
}

impl Confidence {
    pub fn new(score: f64) -> Self {
        Self {
            score,
            level: ConfidenceLevel::from_score(score),
        }
    }

    pub fn get_score(&self) -> f64 {
        self.score
    }

    pub fn get_level(&self) -> ConfidenceLevel {
        self.level
    }
}

impl std::fmt::Display for Confidence {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Confidence[{} at {:.3}%]",
            self.level.to_string(),
            self.score
        )
    }
}
