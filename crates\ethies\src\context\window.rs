use super::ContextDel<PERSON>;
use std::collections::VecDeque;
use std::time::{Duration, Instant};

/// A window is a 10 second sliding context window that
/// will keep the last 10 seconds of play information in the window
/// and feed into the model
pub struct SlidingWindow {
    /// The maximum duration to keep entries in the window
    window_duration: Duration,
    /// Queue of (timestamp, delta) pairs
    entries: VecDeque<(Instant, ContextDelta)>,
}

impl SlidingWindow {
    /// Creates a new sliding window with a 10-second duration
    pub fn new() -> Self {
        Self {
            window_duration: Duration::from_secs(10),
            entries: VecDeque::new(),
        }
    }

    /// Add a new context delta to the window
    pub fn push(&mut self, delta: ContextDelta) {
        let now = Instant::now();
        self.prune(now);
        self.entries.push_back((now, delta));
    }

    /// Remove entries older than the window duration
    fn prune(&mut self, current_time: Instant) {
        for i in 0..self.entries.len() {
            if let Some((time, _)) = self.entries.get(i) {
                if current_time.duration_since(*time) > self.window_duration {
                    self.entries.remove(i);
                }
            }
        }
    }

    /// Get all deltas currently in the window
    pub fn get_deltas(&self) -> impl Iterator<Item = &ContextDelta> {
        self.entries.iter().map(|(_, delta)| delta)
    }

    /// Get the number of entries in the window
    pub fn len(&self) -> usize {
        self.entries.len()
    }

    /// Check if the window is empty
    pub fn is_empty(&self) -> bool {
        self.entries.is_empty()
    }

    /// Clear all entries from the window
    pub fn clear(&mut self) {
        self.entries.clear();
    }
}
