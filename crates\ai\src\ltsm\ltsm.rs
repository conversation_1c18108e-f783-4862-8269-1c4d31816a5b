use super::cell::{CellStates, LtsmCell};
use ndarray::Array2;

pub struct Ltsm {
    cells: Vec<LtsmCell>,
}

impl Ltsm {
    pub fn new(input_size: usize, hidden_size: usize, layers: usize) -> Self {
        let mut cells = Vec::new();
        for _ in 0..layers {
            cells.push(LtsmCell::new(input_size, hidden_size));
        }

        Ltsm { cells }
    }

    pub fn forward(
        &self,
        input: &Array2<f64>,
        hidden_state: &Array2<f64>,
        cell_state: &Array2<f64>,
    ) -> CellStates {
        let mut current_hidden = hidden_state.clone();
        let mut current_cell = cell_state.clone();

        for cell in &self.cells {
            let cell_states = cell.forward(&input, &current_hidden, &current_cell);
            current_hidden = cell_states.hidden.clone();
            current_cell = cell_states.cell.clone();
        }

        CellStates::new(current_hidden, current_cell)
    }

    pub fn backward(
        &self,
        input: &Array2<f64>,
        hidden_states: &[Array2<f64>],
        cell_states: &[Array2<f64>],
        grad_output: &Array2<f64>,
        grad_cell: &Array2<f64>,
    ) -> Vec<(
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
    )> {
        let mut gradients = Vec::new();
        let mut current_grad_output = grad_output.clone();
        let mut current_grad_cell = grad_cell.clone();

        // Iterate through cells in reverse for backpropagation
        for (i, cell) in self.cells.iter().enumerate().rev() {
            let layer_idx = i;
            let hidden_state = if layer_idx > 0 {
                &hidden_states[layer_idx - 1]
            } else {
                &Array2::zeros((cell.hidden_size, 1))
            };

            let cell_state = if layer_idx > 0 {
                &cell_states[layer_idx - 1]
            } else {
                &Array2::zeros((cell.hidden_size, 1))
            };

            let layer_input = if layer_idx == self.cells.len() - 1 {
                input
            } else {
                if hidden_state.shape()[0] != cell.hidden_size {
                    // todo: why are we getting different shapes..?
                    panic!(
                        "Hidden state shape mismatch: expected {}, got {}",
                        cell.hidden_size,
                        hidden_state.shape()[0]
                    );
                }
                hidden_state
            };

            let layer_grads = cell.backward(
                layer_input,
                hidden_state,
                cell_state,
                &current_grad_output,
                &current_grad_cell,
            );

            current_grad_output = layer_grads.4.clone();
            current_grad_cell = layer_grads.5.clone();

            gradients.push(layer_grads);
        }

        gradients.reverse();
        gradients
    }
}

#[cfg(test)]
mod tests {
    use crate::ltsm::cell::CellStates;

    use super::Ltsm;
    use ndarray::arr2;

    #[test]
    fn test_ltsm_forward() {
        let hidden_size = 2;
        let input_size = 3;
        let num_layers = 2;
        let ltsm = Ltsm::new(input_size, hidden_size, num_layers);

        let input = arr2(&[[0.9], [0.2], [-0.3]]);
        let initial_cell_state = CellStates::new(arr2(&[[0.0], [0.0]]), arr2(&[[0.0], [0.0]]));
        let states = ltsm.forward(&input, &initial_cell_state.hidden, &initial_cell_state.cell);

        assert_eq!(states.hidden.shape(), &[hidden_size, 1]);
        assert_eq!(states.cell.shape(), &[hidden_size, 1]);
    }

    // #[test]
    // fn test_ltsm_backward() {
    //     let hidden_size = 2;
    //     let input_size = 3;
    //     let num_layers = 2;
    //     let label_factory = Arc::new(LabelFactory::new());
    //     let ltsm = Ltsm::new(input_size, hidden_size, num_layers, label_factory);

    //     // Forward pass inputs
    //     let input = arr2(&[[0.9], [0.2], [-0.3]]);
    //     let initial_hidden = arr2(&[[0.0], [0.0]]);
    //     let initial_cell = arr2(&[[0.0], [0.0]]);

    //     // Forward pass through each layer to collect states
    //     let mut hidden_states = Vec::new();
    //     let mut cell_states = Vec::new();

    //     // Initial states
    //     let mut current_hidden = initial_hidden.clone();
    //     let mut current_cell = initial_cell.clone();

    //     // Forward pass through each layer
    //     for cell in &ltsm.cells {
    //         // Store the current states before updating
    //         hidden_states.push(current_hidden.clone());
    //         cell_states.push(current_cell.clone());

    //         // Compute new states
    //         let states = cell.forward(&input, &current_hidden, &current_cell);
    //         current_hidden = states.hidden;
    //         current_cell = states.cell;
    //     }

    //     // Gradient inputs (from the last layer)
    //     let grad_output = arr2(&[[0.1], [0.2]]);
    //     let grad_cell = arr2(&[[0.0], [0.0]]);

    //     // Backward pass
    //     let gradients = ltsm.backward(
    //         &input,
    //         &hidden_states,
    //         &cell_states,
    //         &grad_output,
    //         &grad_cell
    //     );

    //     // Verify gradients for each layer
    //     for (i, (grad_w_ih, grad_w_hh, grad_b_ih, grad_b_hh, grad_input, grad_hidden)) in gradients.iter().enumerate() {
    //         // Check input-to-hidden weight gradients
    //         assert_eq!(grad_w_ih.shape(), &[4 * hidden_size, input_size]);

    //         // Check hidden-to-hidden weight gradients
    //         assert_eq!(grad_w_hh.shape(), &[4 * hidden_size, hidden_size]);

    //         // Check bias gradients
    //         assert_eq!(grad_b_ih.shape(), &[4 * hidden_size, 1]);
    //         assert_eq!(grad_b_hh.shape(), &[4 * hidden_size, 1]);

    //         // Check input gradients
    //         if i == num_layers - 1 {
    //             // Last layer should have input_size gradients
    //             assert_eq!(grad_input.shape(), &[input_size, 1]);
    //         } else {
    //             // Other layers should have hidden_size gradients
    //             assert_eq!(grad_input.shape(), &[hidden_size, 1]);
    //         }

    //         // Check hidden state gradients
    //         assert_eq!(grad_hidden.shape(), &[hidden_size, 1]);

    //         // Verify gradients are not all zeros
    //         assert!(!grad_w_ih.iter().all(|&x| x == 0.0), "Weight gradients should not be all zeros");
    //         assert!(!grad_w_hh.iter().all(|&x| x == 0.0), "Weight gradients should not be all zeros");
    //         assert!(!grad_b_ih.iter().all(|&x| x == 0.0), "Bias gradients should not be all zeros");
    //         assert!(!grad_b_hh.iter().all(|&x| x == 0.0), "Bias gradients should not be all zeros");
    //     }
    // }
}
