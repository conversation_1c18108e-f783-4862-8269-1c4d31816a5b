<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

enum MovementState: int {
    case WALK = 0;
    case SNEAK = 1;
    case SPRINT = 2;
    case GLIDE = 3;
    case SWIM = 4;
    case CRAWL = 5;
}

class MovementStateDelta extends ContextDelta {
    
    private MovementState $state;
    private MovementState $previousState;
    private bool $onGround;
    private bool $inWater;
    private bool $inLava;
    private bool $flying;
    
    public function __construct(
        MovementState $state, 
        MovementState $previousState, 
        bool $onGround = true, 
        bool $inWater = false, 
        bool $inLava = false,
        bool $flying = false
    ) {
        $this->state = $state;
        $this->previousState = $previousState;
        $this->onGround = $onGround;
        $this->inWater = $inWater;
        $this->inLava = $inLava;
        $this->flying = $flying;
    }
    
    public function getState(): MovementState {
        return $this->state;
    }
    
    public function getPreviousState(): MovementState {
        return $this->previousState;
    }
    
    public function isOnGround(): bool {
        return $this->onGround;
    }
    
    public function isInWater(): bool {
        return $this->inWater;
    }
    
    public function isInLava(): bool {
        return $this->inLava;
    }
    
    public function isFlying(): bool {
        return $this->flying;
    }
    
    public function parse(): string {
        // Pack as: 1 byte state, 1 byte previous state, 1 byte flags
        $flags = 0;
        if ($this->onGround) $flags |= 0x01;
        if ($this->inWater) $flags |= 0x02;
        if ($this->inLava) $flags |= 0x04;
        if ($this->flying) $flags |= 0x08;
        
        return pack('CCC', $this->state->value, $this->previousState->value, $flags);
    }
    
    public function decode(string $bytes): string {
        $unpacked = unpack('Cstate/CpreviousState/Cflags', $bytes);
        if ($unpacked === false) {
            throw new \InvalidArgumentException("Invalid movement state delta bytes");
        }
        
        $this->state = MovementState::from($unpacked['state']);
        $this->previousState = MovementState::from($unpacked['previousState']);
        
        $flags = $unpacked['flags'];
        $this->onGround = ($flags & 0x01) !== 0;
        $this->inWater = ($flags & 0x02) !== 0;
        $this->inLava = ($flags & 0x04) !== 0;
        $this->flying = ($flags & 0x08) !== 0;
        
        return "MovementStateDelta(state={$this->state->name}, previous={$this->previousState->name}, onGround={$this->onGround}, inWater={$this->inWater}, flying={$this->flying})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::MOVE;
    }
    
    public function hasStateChanged(): bool {
        return $this->state !== $this->previousState;
    }
    
    public function isSneaking(): bool {
        return $this->state === MovementState::SNEAK;
    }
    
    public function isSprinting(): bool {
        return $this->state === MovementState::SPRINT;
    }
    
    public function isWalking(): bool {
        return $this->state === MovementState::WALK;
    }
    
    public function getSpeedMultiplier(): float {
        return match($this->state) {
            MovementState::SNEAK => 0.3,
            MovementState::WALK => 1.0,
            MovementState::SPRINT => 1.3,
            MovementState::SWIM => 0.8,
            MovementState::CRAWL => 0.15,
            MovementState::GLIDE => 1.0,
        };
    }
}
