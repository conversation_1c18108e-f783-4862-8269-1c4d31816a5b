use super::common::{This<PERSON>ntity, This<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Vector3};
use binary_util::BinaryIo;

/// the `ThisEntity` for the attacker is extracted when normalizing.
/// before hitting the models.
/// vec3 = 12
/// interval = 8 bytes
/// facing = 1
/// facing_correction = 12
/// health = 1
/// position = 12 bytes
/// variant = 1 byte
/// entity_kind = 1 byte
/// entity_id = 1 byte
/// rotation = 4 bytes
/// rotation = 4 bytes
/// 57 total -> context
///
/// 16 MAX per delta (Attack)
/// 2 MIN per delta (Attack)
/// 12 AVG per delta
/// 57 intital -> 12 delta (avg)

/// 2400 Per player 10s AVG
/// 3200 Per player 10s zmax
#[derive(Clone, Debug, BinaryIo)]
pub struct Attack {
    /// The other entity's location normalized
    /// this is important, but will be normalized to
    /// attack distance
    pub target: AttackedEntity,
    /// The index + 1 in `targets` of the last entity attacked
    /// This will be normalized to index + 1 for the models,
    /// 0 will mean no entity so `self.targets[last_target - 1]`
    pub last_target: u32,
    /// The velocity of the attacker during the attack
    pub velocity: Vector3,
    /// The ticks that have passed since the `player` has attacked
    /// `target`
    pub interval: u32,
    /// Whether or not the player was facing the target at this time,
    pub facing: bool,
    /// The offset (correction) the player would have had to make to face this player
    pub facing_correction: f32,
}

#[derive(Clone, Debug, BinaryIo)]
pub struct AttackedEntity {
    /// The entity itself
    pub this: ThisEntity,
    /// The health of this entity (THIS IS OPT IN)
    /// out of 100
    pub health: u8,
}

#[derive(Clone, Debug, BinaryIo)]
#[repr(u8)]
pub enum AttackEntityDelta {
    Entity(ThisEntityDelta),
    Health(u8),
}

#[derive(Clone, Debug, BinaryIo)]
#[repr(u8)]
pub enum AttackDelta {
    Target(AttackEntityDelta),
    Velocity(Vector3),
    Interval(u32),
    Facing(bool),
    FacingCorrection(Vector3),
}
