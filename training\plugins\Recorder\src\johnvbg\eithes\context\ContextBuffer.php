<?php

namespace johnvbg\eithes\context;

/** This is an arbitrary limit for the models */
const MAX_BUFFER_SIZE = 6400;

class ContextBuffer {
    /** @var ContextBufferEntry[] - This will always have the last 15 seconds of context deltas */
    private array $buffer;

    /** This is the time the buffer was last reset */
    private float $time;

    public function insert(ContextDelta $delta): void {
        $this->prune();
        array_unshift($this->buffer, $delta);
    }

    private function prune(): void {
        // prune all outdated entries
        foreach ($this->buffer as $k=>$entry) {
            if ($entry->pruneable()) {
                unset($this->buffer[$k]);
            }
        }
    }
}