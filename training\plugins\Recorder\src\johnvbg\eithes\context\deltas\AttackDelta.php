<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class AttackDelta extends ContextDelta {
    
    private AttackEntityDelta $entityData;
    private IntervalDelta $intervalData;
    private FacingDelta $facingData;
    private FacingCorrectionDelta $correctionData;
    private VelocityDelta $attackerVelocity;
    private int $lastTargetId;
    private float $timestamp;
    
    public function __construct(
        AttackEntityDelta $entityData,
        IntervalDelta $intervalData,
        FacingDelta $facingData,
        FacingCorrectionDelta $correctionData,
        VelocityDelta $attackerVelocity,
        int $lastTargetId = 0,
        float $timestamp = null
    ) {
        $this->entityData = $entityData;
        $this->intervalData = $intervalData;
        $this->facingData = $facingData;
        $this->correctionData = $correctionData;
        $this->attackerVelocity = $attackerVelocity;
        $this->lastTargetId = $lastTargetId;
        $this->timestamp = $timestamp ?? microtime(true);
    }
    
    public function getEntityData(): AttackEntityDelta {
        return $this->entityData;
    }
    
    public function getIntervalData(): IntervalDelta {
        return $this->intervalData;
    }
    
    public function getFacingData(): FacingDelta {
        return $this->facingData;
    }
    
    public function getCorrectionData(): FacingCorrectionDelta {
        return $this->correctionData;
    }
    
    public function getAttackerVelocity(): VelocityDelta {
        return $this->attackerVelocity;
    }
    
    public function getLastTargetId(): int {
        return $this->lastTargetId;
    }
    
    public function getTimestamp(): float {
        return $this->timestamp;
    }
    
    public function parse(): string {
        // Pack timestamp and lastTargetId first
        $data = pack('dV', $this->timestamp, $this->lastTargetId);
        
        // Pack each component
        $data .= $this->entityData->parse();
        $data .= $this->intervalData->parse();
        $data .= $this->facingData->parse();
        $data .= $this->correctionData->parse();
        $data .= $this->attackerVelocity->parse();
        
        return $data;
    }
    
    public function decode(string $bytes): string {
        $offset = 0;
        
        // Decode timestamp and lastTargetId
        $header = unpack('dtimestamp/VlastTargetId', substr($bytes, $offset, 12));
        $this->timestamp = $header['timestamp'];
        $this->lastTargetId = $header['lastTargetId'];
        $offset += 12;
        
        // Decode entity data (71 bytes based on AttackEntityDelta structure)
        $entityBytes = substr($bytes, $offset, 71);
        $this->entityData = new AttackEntityDelta(
            new EntityInfo(0, EntityType::UNKNOWN, 0, 0, 0, 0, 0, 0),
            new EntityInfo(0, EntityType::UNKNOWN, 0, 0, 0, 0, 0, 0),
            0
        );
        $this->entityData->decode($entityBytes);
        $offset += 71;
        
        // Decode interval data (variable size, but we know the structure)
        $intervalHeaderSize = 21; // timestamp + basic data
        $intervalHeader = substr($bytes, $offset, $intervalHeaderSize);
        $intervalCount = unpack('C', substr($intervalHeader, 20, 1))[1];
        $intervalSize = $intervalHeaderSize + ($intervalCount * 4);
        $intervalBytes = substr($bytes, $offset, $intervalSize);
        $this->intervalData = new IntervalDelta(0, 0.0);
        $this->intervalData->decode($intervalBytes);
        $offset += $intervalSize;
        
        // Decode facing data (33 bytes)
        $facingBytes = substr($bytes, $offset, 33);
        $this->facingData = new FacingDelta(false, 0, 0, 0, 0, 0);
        $this->facingData->decode($facingBytes);
        $offset += 33;
        
        // Decode correction data (48 bytes)
        $correctionBytes = substr($bytes, $offset, 48);
        $this->correctionData = new FacingCorrectionDelta(0, 0, 0, 0, 0, 0, 0, 0);
        $this->correctionData->decode($correctionBytes);
        $offset += 48;
        
        // Decode velocity data (16 bytes)
        $velocityBytes = substr($bytes, $offset, 16);
        $this->attackerVelocity = new VelocityDelta(0, 0, 0, 0);
        $this->attackerVelocity->decode($velocityBytes);
        
        return "AttackDelta(target={$this->entityData->getTarget()->entityId}, cps={$this->intervalData->getAverageCps()}, facing={$this->facingData->isFacingTarget()})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::ATTACK;
    }
    
    public function isTargetSwitch(): bool {
        return $this->lastTargetId !== 0 && $this->lastTargetId !== $this->entityData->getTarget()->entityId;
    }
    
    public function getOverallSuspicionScore(): float {
        $scores = [
            $this->entityData->getSuspicionScore() * 0.3,      // 30% weight for entity/position
            $this->intervalData->isInhumanCps() ? 0.8 : 0.0,  // High weight for inhuman CPS
            $this->facingData->isLikelyAimbot() ? 0.7 : 0.0,  // High weight for aimbot
            $this->correctionData->getSuspicionLevel() * 0.4,  // 40% weight for corrections
        ];
        
        // Additional factors
        if ($this->isTargetSwitch() && $this->intervalData->getCurrentCps() > 10) {
            $scores[] = 0.3; // Rapid target switching
        }
        
        if ($this->attackerVelocity->getMagnitude() > 0.5 && $this->facingData->isPerfectAim()) {
            $scores[] = 0.4; // Perfect aim while moving fast
        }
        
        return min(1.0, array_sum($scores));
    }
    
    public function getCheatType(): string {
        $suspicion = $this->getOverallSuspicionScore();
        
        if ($suspicion < 0.3) return "LEGITIMATE";
        
        $types = [];
        
        if ($this->intervalData->isInhumanCps()) $types[] = "AUTO_CLICK";
        if ($this->facingData->isLikelyAimbot()) $types[] = "AIMBOT";
        if ($this->correctionData->isLikelyAimAssist()) $types[] = "AIM_ASSIST";
        if ($this->entityData->isLongRangeAttack()) $types[] = "REACH";
        if ($this->entityData->isThroughWalls()) $types[] = "WALL_HACK";
        
        if (empty($types)) {
            return $suspicion > 0.7 ? "SUSPICIOUS" : "QUESTIONABLE";
        }
        
        return implode("+", $types);
    }
    
    public function isLikelyLegitimate(): bool {
        return $this->getOverallSuspicionScore() < 0.3;
    }
    
    public function isDefinitelyCheating(): bool {
        return $this->getOverallSuspicionScore() > 0.8;
    }
    
    public function getConfidenceLevel(): float {
        $suspicion = $this->getOverallSuspicionScore();
        
        // Higher confidence for extreme values
        if ($suspicion > 0.9 || $suspicion < 0.1) return 0.95;
        if ($suspicion > 0.8 || $suspicion < 0.2) return 0.85;
        if ($suspicion > 0.7 || $suspicion < 0.3) return 0.75;
        
        return 0.6; // Medium confidence for borderline cases
    }
    
    public function getAttackQuality(): array {
        return [
            'accuracy' => $this->facingData->getAccuracyScore(),
            'timing' => 1.0 - ($this->intervalData->isHighCps() ? 0.5 : 0.0),
            'positioning' => 1.0 - $this->entityData->getSuspicionScore(),
            'movement' => $this->attackerVelocity->getMagnitude() < 0.3 ? 1.0 : 0.7,
        ];
    }
    
    public function toArray(): array {
        return [
            'timestamp' => $this->timestamp,
            'lastTargetId' => $this->lastTargetId,
            'targetSwitch' => $this->isTargetSwitch(),
            'entity' => $this->entityData->toArray(),
            'interval' => $this->intervalData->toArray(),
            'facing' => $this->facingData->toArray(),
            'correction' => $this->correctionData->toArray(),
            'velocity' => [
                'x' => $this->attackerVelocity->getX(),
                'y' => $this->attackerVelocity->getY(),
                'z' => $this->attackerVelocity->getZ(),
                'magnitude' => $this->attackerVelocity->getMagnitude(),
                'ticks' => $this->attackerVelocity->getTicks(),
            ],
            'analysis' => [
                'suspicionScore' => $this->getOverallSuspicionScore(),
                'cheatType' => $this->getCheatType(),
                'legitimate' => $this->isLikelyLegitimate(),
                'definitelyCheating' => $this->isDefinitelyCheating(),
                'confidenceLevel' => $this->getConfidenceLevel(),
                'quality' => $this->getAttackQuality(),
            ]
        ];
    }
}
