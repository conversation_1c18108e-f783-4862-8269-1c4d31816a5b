use ndarray::{Array2, Axis};
use std::collections::HashMap;

pub type Label = usize;

pub struct LabeledData {
    pub label: String,
    sequence: Array2<f64>,
}

impl LabeledData {
    pub fn new(label: &str, data: Array2<f64>, normalize: bool) -> Self {
        let mut s = Self {
            label: label.to_string(),
            sequence: data,
        };

        if normalize {
            s.normalize();
        }
        s
    }

    pub fn get_sequence(&self) -> &Array2<f64> {
        &self.sequence
    }

    fn normalize(&mut self) {
        // Get min and max values across all elements
        let mut min = f64::INFINITY;
        let mut max = f64::NEG_INFINITY;

        for &val in self.sequence.iter() {
            if val < min {
                min = val;
            }
            if val > max {
                max = val;
            }
        }

        let range = if max == min { 1.0 } else { max - min };

        // Normalize each value to [0,1] range
        self.sequence.mapv_inplace(|x| (x - min) / range);
    }
}

/// Holds all labels for the model
pub struct LabelFactory {
    indices: HashMap<usize, String>,
}

impl LabelFactory {
    pub fn new() -> Self {
        Self {
            indices: HashMap::new(),
        }
    }

    pub fn get_label(&self, id: &usize) -> Option<String> {
        self.indices.get(&id).cloned()
    }

    pub fn find_one(&self, name: &str) -> Option<usize> {
        self.indices
            .iter()
            .find(|(_, n)| *n == name)
            .map(|(id, _)| *id)
    }

    pub fn add_label(&mut self, name: &str) -> usize {
        if let Some(id) = self.find_one(name) {
            // we already have this label
            return id;
        }

        let id = self.indices.len();
        self.indices.insert(id, name.to_string());
        id
    }
}
