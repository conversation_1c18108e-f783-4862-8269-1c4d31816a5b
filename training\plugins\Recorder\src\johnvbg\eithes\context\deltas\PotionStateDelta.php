<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

enum PotionType: int {
    case SWIFTNESS = 0;
    case SLOWNESS = 1;
    case BLINDNESS = 2;
    case SLOW_FALL = 3;
    case LEVITATION = 4;
    case LEAPING = 5;
    case POISON = 6;
    case HARMING = 7;
    case WIND_CHARGE = 8;
    case STRENGTH = 9;
    case WEAKNESS = 10;
    case REGENERATION = 11;
    case ABSORPTION = 12;
    case NONE = 255;
}

class PotionEffect {
    public PotionType $type;
    public int $level;
    public int $duration; // in ticks
    public bool $visible;
    
    public function __construct(PotionType $type, int $level, int $duration, bool $visible = true) {
        $this->type = $type;
        $this->level = $level;
        $this->duration = $duration;
        $this->visible = $visible;
    }
    
    public function isActive(): bool {
        return $this->duration > 0;
    }
    
    public function getMultiplier(): float {
        return match($this->type) {
            PotionType::SWIFTNESS => 1.0 + (0.2 * $this->level),
            PotionType::SLOWNESS => 1.0 - (0.15 * $this->level),
            PotionType::LEAPING => 1.0 + (0.5 * $this->level),
            PotionType::LEVITATION => 1.0,
            PotionType::SLOW_FALL => 0.1,
            default => 1.0,
        };
    }
}

class PotionStateDelta extends ContextDelta {
    
    /** @var PotionEffect[] */
    private array $activeEffects;
    /** @var PotionEffect[] */
    private array $previousEffects;
    
    public function __construct(array $activeEffects = [], array $previousEffects = []) {
        $this->activeEffects = $activeEffects;
        $this->previousEffects = $previousEffects;
    }
    
    public function getActiveEffects(): array {
        return $this->activeEffects;
    }
    
    public function getPreviousEffects(): array {
        return $this->previousEffects;
    }
    
    public function addEffect(PotionEffect $effect): void {
        $this->activeEffects[] = $effect;
    }
    
    public function hasEffect(PotionType $type): bool {
        foreach ($this->activeEffects as $effect) {
            if ($effect->type === $type && $effect->isActive()) {
                return true;
            }
        }
        return false;
    }
    
    public function getEffect(PotionType $type): ?PotionEffect {
        foreach ($this->activeEffects as $effect) {
            if ($effect->type === $type && $effect->isActive()) {
                return $effect;
            }
        }
        return null;
    }
    
    public function parse(): string {
        $data = pack('C', count($this->activeEffects));
        
        foreach ($this->activeEffects as $effect) {
            $flags = $effect->visible ? 0x01 : 0x00;
            $data .= pack('CCVVC', 
                $effect->type->value, 
                $effect->level, 
                $effect->duration, 
                0, // padding
                $flags
            );
        }
        
        return $data;
    }
    
    public function decode(string $bytes): string {
        $offset = 0;
        $count = unpack('C', substr($bytes, $offset, 1))[1];
        $offset += 1;
        
        $this->activeEffects = [];
        
        for ($i = 0; $i < $count; $i++) {
            $effectData = unpack('Ctype/Clevel/Vduration/Vpadding/Cflags', substr($bytes, $offset, 11));
            $offset += 11;
            
            if ($effectData === false) {
                throw new \InvalidArgumentException("Invalid potion state delta bytes");
            }
            
            $type = PotionType::from($effectData['type']);
            $level = $effectData['level'];
            $duration = $effectData['duration'];
            $visible = ($effectData['flags'] & 0x01) !== 0;
            
            $this->activeEffects[] = new PotionEffect($type, $level, $duration, $visible);
        }
        
        $effectNames = array_map(fn($e) => $e->type->name . ":" . $e->level, $this->activeEffects);
        return "PotionStateDelta(effects=[" . implode(", ", $effectNames) . "])";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::MOVE;
    }
    
    public function getMovementMultiplier(): float {
        $multiplier = 1.0;
        
        foreach ($this->activeEffects as $effect) {
            if (!$effect->isActive()) continue;
            
            switch ($effect->type) {
                case PotionType::SWIFTNESS:
                    $multiplier *= $effect->getMultiplier();
                    break;
                case PotionType::SLOWNESS:
                    $multiplier *= $effect->getMultiplier();
                    break;
            }
        }
        
        return $multiplier;
    }
    
    public function getJumpMultiplier(): float {
        $effect = $this->getEffect(PotionType::LEAPING);
        return $effect ? $effect->getMultiplier() : 1.0;
    }
    
    public function hasMovementAffectingEffects(): bool {
        return $this->hasEffect(PotionType::SWIFTNESS) || 
               $this->hasEffect(PotionType::SLOWNESS) ||
               $this->hasEffect(PotionType::LEAPING) ||
               $this->hasEffect(PotionType::LEVITATION) ||
               $this->hasEffect(PotionType::SLOW_FALL);
    }
}
