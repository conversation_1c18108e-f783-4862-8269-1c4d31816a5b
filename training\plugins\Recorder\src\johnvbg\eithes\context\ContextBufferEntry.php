<?php

namespace johnvbg\eithes\context;

use johnvbg\eithes\context\ContextDelta;

class ContextBufferEntry {

    /** @var float $time = this is the time that the entry entered the buffer */
    private float $time;
    /** The actual entry */
    private ContextDelta $item;

    public function __construct(ContextDelta $item) {
        $this->item = $item;
        $this->time = microtime(true);
    }

    public function getItem(): ContextDelta {
        return $this->item;
    }

    public function pruneable(): bool {
        $delta = microtime(true) - $this->time;
        return $delta >= 15;
    }

}