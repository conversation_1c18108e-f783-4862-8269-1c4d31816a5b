use binary_util::BinaryIo;

/// Vector 3 is normalized before sending to the context,
/// All contexts take relative player data and assume the position is 0 0 0 when the context begins
/// the player distance is then calculated and transformed from the previous distance.
#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
pub struct Vector3 {
    x: f32,
    y: f32,
    z: f32,
}

/// A universal label to track entities without retaining a ton of metadata.
#[derive(<PERSON>lone, Debug, BinaryIo)]
pub struct EntityId(u32);

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
pub struct ThisEntity {
    /// The position of this entity, usually the player
    pub position: Vector3,
    /// The targets type
    pub variant: EntityKind,
    pub id: EntityId,
    pub head_rotation: Rotation,
}

#[derive(<PERSON>lone, Debug, BinaryIo)]
#[repr(u8)]
pub enum ThisEntityDelta {
    Position(Vector3),
    Variant(EntityKind),
    HeadRotation(Rotation),
}

/// Yaw and pitch are normalized
#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ary<PERSON><PERSON>)]
pub struct Rotation {
    pub yaw: f32,
    pub pitch: f32,
}

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
#[repr(u8)]
pub enum EntityKind {
    Player,
    Mob,
    Text,
    Unknown,
}

#[derive(Clone, Debug, BinaryIo)]
#[repr(u8)]
pub enum CollisionState {
    Allow,
    Disallow,
    None,
}

#[derive(Clone, Debug, BinaryIo)]
pub struct CollisionLayer {
    t_left_1: CollisionState,
    t_left_2: CollisionState,
    t_middle: CollisionState,
    t_right_1: CollisionState,
    t_right_2: CollisionState,
    tm_left_1: CollisionState,
    tm_left_2: CollisionState,
    tm_middle: CollisionState,
    tm_right_1: CollisionState,
    tm_right_2: CollisionState,
    m_left_1: CollisionState,
    m_left_2: CollisionState,
    m_middle: CollisionState,
    m_right_1: CollisionState,
    m_right_2: CollisionState,
    bm_left_1: CollisionState,
    bm_left_2: CollisionState,
    bm_middle: CollisionState,
    bm_right_1: CollisionState,
    bm_right_2: CollisionState,
    b_left_1: CollisionState,
    b_left_2: CollisionState,
    b_middle: CollisionState,
    b_right_1: CollisionState,
    b_right_2: CollisionState,
}

#[derive(Clone, Debug, BinaryIo)]
pub struct CollisionBuffer {
    pub buffer: Vec<CollisionLayer>,
    pub is_colliding: bool,
}
