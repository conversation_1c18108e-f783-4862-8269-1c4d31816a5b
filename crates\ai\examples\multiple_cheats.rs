use ai::ltsm::{labels::LabeledData, network::Network};
use ndarray::arr2;

fn main() {
    println!("beginnging??");

    // Create training data for different cheat types with more distinct values
    let kill_aura1 = arr2(&[[-10.0], [-10.0]]);
    let kill_aura2 = arr2(&[[-9.0], [-9.5]]);
    let kill_aura3 = arr2(&[[-9.5], [-9.0]]);

    // Vanilla patterns (normal player movements) - More diverse range
    let vanilla1 = arr2(&[[1.0], [1.0]]);
    let vanilla2 = arr2(&[[-1.0], [0.6]]);
    let vanilla3 = arr2(&[[0.6], [-1.0]]);

    // Flight patterns (unusual vertical movements) - More diverse patterns
    let flight1 = arr2(&[[20.0], [20.0]]);
    let flight2 = arr2(&[[-18.0], [18.0]]);
    let flight3 = arr2(&[[18.0], [-18.0]]);

    // Print values for verification
    println!("Kill Aura 1: {:?}", kill_aura1);
    println!("Vanilla 1: {:?}", vanilla1);
    println!("Flight 1: {:?}", flight1);

    // Create labeled data without normalization
    let data_kill_aura1 = LabeledData::new("cheat.killaura", kill_aura1.clone(), false);
    let data_kill_aura2 = LabeledData::new("cheat.killaura", kill_aura2.clone(), false);
    let data_kill_aura3 = LabeledData::new("cheat.killaura", kill_aura3.clone(), false);

    println!("vanilla");
    let data_vanilla1 = LabeledData::new("vanilla", vanilla1.clone(), false);
    let data_vanilla2 = LabeledData::new("vanilla", vanilla2.clone(), false);
    let data_vanilla3 = LabeledData::new("vanilla", vanilla3.clone(), false);

    println!("flight");
    let data_flight1 = LabeledData::new("cheat.flight", flight1.clone(), false);
    let data_flight2 = LabeledData::new("cheat.flight", flight2.clone(), false);
    let data_flight3 = LabeledData::new("cheat.flight", flight3.clone(), false);

    // Increase learning rate significantly
    let mut network = Network::new(2, 64, 3, Some(0.3));

    // Collect all data first
    let all_data = vec![
        kill_aura1.clone(),
        kill_aura2.clone(),
        kill_aura3.clone(),
        vanilla1.clone(),
        vanilla2.clone(),
        vanilla3.clone(),
        flight1.clone(),
        flight2.clone(),
        flight3.clone(),
    ];

    // Find global min and max
    let mut min = f64::INFINITY;
    let mut max = f64::NEG_INFINITY;
    for data in &all_data {
        for &val in data.iter() {
            if val < min {
                min = val;
            }
            if val > max {
                max = val;
            }
        }
    }

    let mut prev_loss = f64::INFINITY;
    for i in 0..100 {
        let mut total_loss = 0.0;

        total_loss += network.train(&data_kill_aura1);
        total_loss += network.train(&data_kill_aura2);
        total_loss += network.train(&data_kill_aura3);
        total_loss += network.train(&data_vanilla1);
        total_loss += network.train(&data_vanilla2);
        total_loss += network.train(&data_vanilla3);
        total_loss += network.train(&data_flight1);
        total_loss += network.train(&data_flight2);
        total_loss += network.train(&data_flight3);

        if i % 10 == 0 {
            // Print less frequently
            println!(
                "\n\nTRAINING: Iteration {} - Average Loss: {}",
                i,
                total_loss / 9.0
            );
            let pred_ka1 = network.predict(data_kill_aura1.get_sequence());
            let pred_v1 = network.predict(data_vanilla1.get_sequence());
            let pred_f1 = network.predict(data_flight2.get_sequence());

            println!(
                "Kill Aura: {} (prob: {}, conf: {})",
                pred_ka1.get_label(),
                pred_ka1.get_output()[[0, 0]],
                pred_ka1.get_confidence()
            );
            println!(
                "Vanilla: {} (prob: {}, conf: {})",
                pred_v1.get_label(),
                pred_v1.get_output()[[0, 0]],
                pred_v1.get_confidence()
            );
            println!(
                "Flight: {} (prob: {}, conf: {})",
                pred_f1.get_label(),
                pred_f1.get_output()[[0, 0]],
                pred_f1.get_confidence()
            );
        }

        assert!(total_loss.is_finite(), "Loss should be finite");
        if i > 0 {
            assert!(
                total_loss <= prev_loss * 4.4,
                "Loss should generally decrease"
            );
        }
        prev_loss = total_loss;
    }

    // Test predictions using normalized inputs
    println!("\nTesting Kill Aura patterns:");
    let pred_ka1 = network.predict(data_kill_aura1.get_sequence());
    let pred_ka2 = network.predict(data_kill_aura2.get_sequence());
    let pred_ka3 = network.predict(data_kill_aura3.get_sequence());
    println!(
        "Kill Aura 1: {} (prob: {}, conf: {})",
        pred_ka1.get_label(),
        pred_ka1.get_output()[[0, 0]],
        pred_ka1.get_confidence()
    );
    println!(
        "Kill Aura 2: {} (prob: {}, conf: {})",
        pred_ka2.get_label(),
        pred_ka2.get_output()[[0, 0]],
        pred_ka2.get_confidence()
    );
    println!(
        "Kill Aura 3: {} (prob: {}, conf: {})",
        pred_ka3.get_label(),
        pred_ka3.get_output()[[0, 0]],
        pred_ka3.get_confidence()
    );

    println!("\nTesting Vanilla patterns:");
    let pred_v1 = network.predict(data_vanilla1.get_sequence());
    let pred_v2 = network.predict(data_vanilla2.get_sequence());
    let pred_v3 = network.predict(data_vanilla3.get_sequence());
    println!(
        "Vanilla 1: {} (prob: {}, conf: {})",
        pred_v1.get_label(),
        pred_v1.get_output()[[0, 0]],
        pred_v1.get_confidence()
    );
    println!(
        "Vanilla 2: {} (prob: {}, conf: {})",
        pred_v2.get_label(),
        pred_v2.get_output()[[0, 0]],
        pred_v2.get_confidence()
    );
    println!(
        "Vanilla 3: {} (prob: {}, conf: {})",
        pred_v3.get_label(),
        pred_v3.get_output()[[0, 0]],
        pred_v3.get_confidence()
    );

    println!("\nTesting Flight patterns:");
    let pred_f1 = network.predict(data_flight1.get_sequence());
    let pred_f2 = network.predict(data_flight2.get_sequence());
    let pred_f3 = network.predict(data_flight3.get_sequence());
    println!(
        "Flight 1: {} (prob: {}, conf: {})",
        pred_f1.get_label(),
        pred_f1.get_output()[[0, 0]],
        pred_f1.get_confidence()
    );
    println!(
        "Flight 2: {} (prob: {}, conf: {})",
        pred_f2.get_label(),
        pred_f2.get_output()[[0, 0]],
        pred_f2.get_confidence()
    );
    println!(
        "Flight 3: {} (prob: {}, conf: {})",
        pred_f3.get_label(),
        pred_f3.get_output()[[0, 0]],
        pred_f3.get_confidence()
    );

    // Verify that similar patterns give similar predictions
    assert!(
        (pred_ka1.get_output()[[0, 0]] - pred_ka2.get_output()[[0, 0]]).abs() < 0.2,
        "Similar Kill Aura patterns should give similar predictions"
    );
    assert!(
        (pred_v1.get_output()[[0, 0]] - pred_v2.get_output()[[0, 0]]).abs() < 0.2,
        "Similar Vanilla patterns should give similar predictions"
    );
    assert!(
        (pred_f1.get_output()[[0, 0]] - pred_f2.get_output()[[0, 0]]).abs() < 0.2,
        "Similar Flight patterns should give similar predictions"
    );

    // Additional verification for flight patterns
    assert!(
        pred_f1.get_label().contains("flight"),
        "Flight pattern 1 should be classified as flight"
    );
    assert!(
        pred_f2.get_label().contains("flight"),
        "Flight pattern 2 should be classified as flight"
    );
    assert!(
        pred_f3.get_label().contains("flight"),
        "Flight pattern 3 should be classified as flight"
    );
    assert!(
        pred_f1.get_confidence().get_score() > 0.7,
        "Flight pattern 1 should have high confidence"
    );
    assert!(
        pred_f2.get_confidence().get_score() > 0.7,
        "Flight pattern 2 should have high confidence"
    );
    assert!(
        pred_f3.get_confidence().get_score() > 0.7,
        "Flight pattern 3 should have high confidence"
    );
}
