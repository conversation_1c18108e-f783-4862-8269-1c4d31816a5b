pub(crate) mod attack;
pub(crate) mod common;
pub(crate) mod movement;
pub(crate) mod window;

use attack::AttackDel<PERSON>;
use binary_util::BinaryIo;
use common::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Vector3};
use movement::{MovementDelta, MovementState};

/// Represents a change in context that needs to be tracked
#[derive(<PERSON>lone, BinaryIo)]
#[repr(u8)]
pub enum ContextDelta {
    Move(MovementDelta),
    Attack(AttackDelta),
    // Collision(CollisionDelta),
}
