<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class MovementDelta extends ContextDelta {
    
    private VelocityDelta $velocity;
    private PositionDelta $position;
    private MovementStateDelta $state;
    private PotionStateDelta $potionState;
    private float $timestamp;
    
    public function __construct(
        VelocityDelta $velocity,
        PositionDelta $position,
        MovementStateDelta $state,
        PotionStateDelta $potionState,
        float $timestamp = null
    ) {
        $this->velocity = $velocity;
        $this->position = $position;
        $this->state = $state;
        $this->potionState = $potionState;
        $this->timestamp = $timestamp ?? microtime(true);
    }
    
    public function getVelocity(): VelocityDelta {
        return $this->velocity;
    }
    
    public function getPosition(): PositionDelta {
        return $this->position;
    }
    
    public function getState(): MovementStateDelta {
        return $this->state;
    }
    
    public function getPotionState(): PotionStateDelta {
        return $this->potionState;
    }
    
    public function getTimestamp(): float {
        return $this->timestamp;
    }
    
    public function parse(): string {
        // Pack timestamp first, then each component
        $data = pack('d', $this->timestamp); // 8 bytes for double timestamp
        $data .= $this->velocity->parse();
        $data .= $this->position->parse();
        $data .= $this->state->parse();
        $data .= $this->potionState->parse();
        
        return $data;
    }
    
    public function decode(string $bytes): string {
        $offset = 0;
        
        // Decode timestamp
        $timestamp = unpack('d', substr($bytes, $offset, 8))[1];
        $this->timestamp = $timestamp;
        $offset += 8;
        
        // Decode velocity (16 bytes: 3 floats + 1 int)
        $velocityBytes = substr($bytes, $offset, 16);
        $this->velocity = new VelocityDelta(0, 0, 0, 0);
        $this->velocity->decode($velocityBytes);
        $offset += 16;
        
        // Decode position (24 bytes: 6 floats)
        $positionBytes = substr($bytes, $offset, 24);
        $this->position = new PositionDelta(0, 0, 0, 0, 0, 0);
        $this->position->decode($positionBytes);
        $offset += 24;
        
        // Decode state (3 bytes)
        $stateBytes = substr($bytes, $offset, 3);
        $this->state = new MovementStateDelta(MovementState::WALK, MovementState::WALK);
        $this->state->decode($stateBytes);
        $offset += 3;
        
        // Decode potion state (remaining bytes)
        $potionBytes = substr($bytes, $offset);
        $this->potionState = new PotionStateDelta();
        $this->potionState->decode($potionBytes);
        
        return "MovementDelta(timestamp={$this->timestamp}, velocity={$this->velocity->decode($velocityBytes)}, position={$this->position->decode($positionBytes)})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::MOVE;
    }
    
    public function isSignificantMovement(): bool {
        return $this->position->hasMoved() || $this->velocity->isMoving();
    }
    
    public function getExpectedVelocity(): float {
        $baseSpeed = match($this->state->getState()) {
            MovementState::SNEAK => 0.3,
            MovementState::WALK => 1.0,
            MovementState::SPRINT => 1.3,
            MovementState::SWIM => 0.8,
            MovementState::CRAWL => 0.15,
            default => 1.0,
        };
        
        return $baseSpeed * $this->potionState->getMovementMultiplier();
    }
    
    public function getVelocityRatio(): float {
        $expected = $this->getExpectedVelocity();
        $actual = $this->velocity->getHorizontalMagnitude();
        
        return $expected > 0 ? $actual / $expected : 0.0;
    }
    
    public function isPotentiallyAbnormal(): bool {
        $ratio = $this->getVelocityRatio();
        
        // Flag if velocity is significantly higher than expected
        if ($ratio > 1.5) return true;
        
        // Flag if moving while sneaking at sprint speed
        if ($this->state->isSneaking() && $this->velocity->getHorizontalMagnitude() > 0.5) return true;
        
        // Flag if flying without permission
        if ($this->state->isFlying() && !$this->state->isOnGround()) return true;
        
        return false;
    }
    
    public function getAirTime(): int {
        return $this->state->isOnGround() ? 0 : $this->velocity->getTicks();
    }
    
    public function toArray(): array {
        return [
            'timestamp' => $this->timestamp,
            'velocity' => [
                'x' => $this->velocity->getX(),
                'y' => $this->velocity->getY(),
                'z' => $this->velocity->getZ(),
                'ticks' => $this->velocity->getTicks(),
                'magnitude' => $this->velocity->getMagnitude(),
            ],
            'position' => [
                'x' => $this->position->getX(),
                'y' => $this->position->getY(),
                'z' => $this->position->getZ(),
                'deltaX' => $this->position->getDeltaX(),
                'deltaY' => $this->position->getDeltaY(),
                'deltaZ' => $this->position->getDeltaZ(),
                'distance' => $this->position->getDistance(),
            ],
            'state' => [
                'current' => $this->state->getState()->name,
                'previous' => $this->state->getPreviousState()->name,
                'onGround' => $this->state->isOnGround(),
                'inWater' => $this->state->isInWater(),
                'flying' => $this->state->isFlying(),
            ],
            'potions' => array_map(fn($effect) => [
                'type' => $effect->type->name,
                'level' => $effect->level,
                'duration' => $effect->duration,
            ], $this->potionState->getActiveEffects()),
            'analysis' => [
                'expectedVelocity' => $this->getExpectedVelocity(),
                'velocityRatio' => $this->getVelocityRatio(),
                'abnormal' => $this->isPotentiallyAbnormal(),
                'airTime' => $this->getAirTime(),
            ]
        ];
    }
}
