use ndarray::Array2;
use ndarray::Axis;

#[test]
fn test_labeled_data() {
    // Create labeled data from raw features and labels
    let cheat_features = vec![
        // Cheat samples (high values)
        vec![1.0, 0.8, 0.9],
        vec![0.95, 0.7, 0.85],
        vec![0.85, 0.9, 0.7],
        vec![0.92, 0.75, 0.8],
        vec![0.9, 0.8, 0.85],
        vec![0.85, 0.75, 0.9],
        vec![0.95, 0.85, 0.8],
        vec![0.88, 0.82, 0.87],
    ];

    let vanilla_features = vec![
        // Vanilla samples (low values)
        vec![0.2, 0.1, 0.15],
        vec![0.15, 0.2, 0.1],
        vec![0.1, 0.15, 0.2],
        vec![0.25, 0.2, 0.15],
        vec![0.18, 0.12, 0.15],
        vec![0.22, 0.18, 0.12],
        vec![0.15, 0.25, 0.2],
        vec![0.2, 0.15, 0.18],
    ];
}
