<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class FacingCorrectionDelta extends ContextDelta {
    
    private float $correctionX;
    private float $correctionY;
    private float $correctionZ;
    private float $correctionMagnitude;
    private float $correctionTime;
    private float $previousYaw;
    private float $previousPitch;
    private float $newYaw;
    private float $newPitch;
    private float $timestamp;
    
    public function __construct(
        float $correctionX,
        float $correctionY,
        float $correctionZ,
        float $correctionTime,
        float $previousYaw,
        float $previousPitch,
        float $newYaw,
        float $newPitch,
        float $timestamp = null
    ) {
        $this->correctionX = $correctionX;
        $this->correctionY = $correctionY;
        $this->correctionZ = $correctionZ;
        $this->correctionTime = $correctionTime;
        $this->previousYaw = $previousYaw;
        $this->previousPitch = $previousPitch;
        $this->newYaw = $newYaw;
        $this->newPitch = $newPitch;
        $this->timestamp = $timestamp ?? microtime(true);
        $this->calculateMagnitude();
    }
    
    public function getCorrectionX(): float {
        return $this->correctionX;
    }
    
    public function getCorrectionY(): float {
        return $this->correctionY;
    }
    
    public function getCorrectionZ(): float {
        return $this->correctionZ;
    }
    
    public function getCorrectionMagnitude(): float {
        return $this->correctionMagnitude;
    }
    
    public function getCorrectionTime(): float {
        return $this->correctionTime;
    }
    
    public function getPreviousYaw(): float {
        return $this->previousYaw;
    }
    
    public function getPreviousPitch(): float {
        return $this->previousPitch;
    }
    
    public function getNewYaw(): float {
        return $this->newYaw;
    }
    
    public function getNewPitch(): float {
        return $this->newPitch;
    }
    
    public function getTimestamp(): float {
        return $this->timestamp;
    }
    
    private function calculateMagnitude(): void {
        $this->correctionMagnitude = sqrt(
            $this->correctionX * $this->correctionX +
            $this->correctionY * $this->correctionY +
            $this->correctionZ * $this->correctionZ
        );
    }
    
    public function parse(): string {
        // Pack: timestamp(8), corrections(12), correctionTime(4), angles(16), magnitude(4)
        return pack('dfffffffffff', 
            $this->timestamp,
            $this->correctionX,
            $this->correctionY,
            $this->correctionZ,
            $this->correctionTime,
            $this->previousYaw,
            $this->previousPitch,
            $this->newYaw,
            $this->newPitch,
            $this->correctionMagnitude
        );
    }
    
    public function decode(string $bytes): string {
        $unpacked = unpack('dtimestamp/fcorrX/fcorrY/fcorrZ/fcorrTime/fprevYaw/fprevPitch/fnewYaw/fnewPitch/fmagnitude', $bytes);
        if ($unpacked === false) {
            throw new \InvalidArgumentException("Invalid facing correction delta bytes");
        }
        
        $this->timestamp = $unpacked['timestamp'];
        $this->correctionX = $unpacked['corrX'];
        $this->correctionY = $unpacked['corrY'];
        $this->correctionZ = $unpacked['corrZ'];
        $this->correctionTime = $unpacked['corrTime'];
        $this->previousYaw = $unpacked['prevYaw'];
        $this->previousPitch = $unpacked['prevPitch'];
        $this->newYaw = $unpacked['newYaw'];
        $this->newPitch = $unpacked['newPitch'];
        $this->correctionMagnitude = $unpacked['magnitude'];
        
        return "FacingCorrectionDelta(magnitude={$this->correctionMagnitude}, time={$this->correctionTime}ms)";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::ATTACK;
    }
    
    public function getYawCorrection(): float {
        return $this->normalizeAngle($this->newYaw - $this->previousYaw);
    }
    
    public function getPitchCorrection(): float {
        return $this->newPitch - $this->previousPitch;
    }
    
    private function normalizeAngle(float $angle): float {
        while ($angle > 180) $angle -= 360;
        while ($angle < -180) $angle += 360;
        return $angle;
    }
    
    public function getCorrectionSpeed(): float {
        // Degrees per millisecond
        return $this->correctionTime > 0 ? $this->correctionMagnitude / $this->correctionTime : 0.0;
    }
    
    public function isInstantCorrection(): bool {
        // Correction happened in less than 50ms (inhuman reaction time)
        return $this->correctionTime < 50.0;
    }
    
    public function isLargeCorrection(): bool {
        // Correction is larger than 45 degrees
        return $this->correctionMagnitude > 45.0;
    }
    
    public function isPerfectCorrection(): bool {
        // Correction magnitude is suspiciously close to what's needed
        return $this->correctionMagnitude > 10.0 && $this->correctionMagnitude < 180.0;
    }
    
    public function isSnapCorrection(): bool {
        // Large correction in very short time
        return $this->isLargeCorrection() && $this->correctionTime < 100.0;
    }
    
    public function getCorrectionType(): string {
        if ($this->isInstantCorrection()) return "INSTANT";
        if ($this->isSnapCorrection()) return "SNAP";
        if ($this->isLargeCorrection()) return "LARGE";
        if ($this->correctionMagnitude < 5.0) return "MICRO";
        return "NORMAL";
    }
    
    public function getSuspicionLevel(): float {
        $suspicion = 0.0;
        
        // Instant corrections are highly suspicious
        if ($this->isInstantCorrection()) $suspicion += 0.8;
        
        // Large corrections in short time
        if ($this->isSnapCorrection()) $suspicion += 0.6;
        
        // Perfect corrections (not too small, not too large)
        if ($this->isPerfectCorrection()) $suspicion += 0.4;
        
        // Very high correction speed
        $speed = $this->getCorrectionSpeed();
        if ($speed > 5.0) $suspicion += 0.3; // 5 degrees per ms is very fast
        
        // Normalize to 0-1 range
        return min(1.0, $suspicion);
    }
    
    public function isLikelyAimAssist(): bool {
        return $this->getSuspicionLevel() > 0.7;
    }
    
    public function getHumanLikelihood(): float {
        // Inverse of suspicion level
        return 1.0 - $this->getSuspicionLevel();
    }
    
    public function getCorrectionVector(): array {
        return [
            'x' => $this->correctionX,
            'y' => $this->correctionY,
            'z' => $this->correctionZ,
        ];
    }
    
    public function getAngularVelocity(): float {
        // Degrees per second
        return $this->correctionTime > 0 ? ($this->correctionMagnitude / $this->correctionTime) * 1000.0 : 0.0;
    }
    
    public function toArray(): array {
        return [
            'timestamp' => $this->timestamp,
            'correction' => [
                'x' => $this->correctionX,
                'y' => $this->correctionY,
                'z' => $this->correctionZ,
                'magnitude' => $this->correctionMagnitude,
                'time' => $this->correctionTime,
            ],
            'angles' => [
                'previousYaw' => $this->previousYaw,
                'previousPitch' => $this->previousPitch,
                'newYaw' => $this->newYaw,
                'newPitch' => $this->newPitch,
                'yawCorrection' => $this->getYawCorrection(),
                'pitchCorrection' => $this->getPitchCorrection(),
            ],
            'analysis' => [
                'type' => $this->getCorrectionType(),
                'speed' => $this->getCorrectionSpeed(),
                'angularVelocity' => $this->getAngularVelocity(),
                'suspicionLevel' => $this->getSuspicionLevel(),
                'humanLikelihood' => $this->getHumanLikelihood(),
                'likelyAimAssist' => $this->isLikelyAimAssist(),
                'instant' => $this->isInstantCorrection(),
                'snap' => $this->isSnapCorrection(),
                'large' => $this->isLargeCorrection(),
                'perfect' => $this->isPerfectCorrection(),
            ]
        ];
    }
}
