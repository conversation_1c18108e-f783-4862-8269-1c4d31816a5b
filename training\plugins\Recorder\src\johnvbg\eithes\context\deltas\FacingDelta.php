<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class FacingDelta extends ContextDelta {
    
    private bool $facingTarget;
    private float $playerYaw;
    private float $playerPitch;
    private float $targetYaw;
    private float $targetPitch;
    private float $angleDifference;
    private float $distance;
    private float $timestamp;
    
    public function __construct(
        bool $facingTarget,
        float $playerYaw,
        float $playerPitch,
        float $targetYaw,
        float $targetPitch,
        float $distance,
        float $timestamp = null
    ) {
        $this->facingTarget = $facingTarget;
        $this->playerYaw = $playerYaw;
        $this->playerPitch = $playerPitch;
        $this->targetYaw = $targetYaw;
        $this->targetPitch = $targetPitch;
        $this->distance = $distance;
        $this->timestamp = $timestamp ?? microtime(true);
        $this->calculateAngleDifference();
    }
    
    public function isFacingTarget(): bool {
        return $this->facingTarget;
    }
    
    public function getPlayerYaw(): float {
        return $this->playerYaw;
    }
    
    public function getPlayerPitch(): float {
        return $this->playerPitch;
    }
    
    public function getTargetYaw(): float {
        return $this->targetYaw;
    }
    
    public function getTargetPitch(): float {
        return $this->targetPitch;
    }
    
    public function getAngleDifference(): float {
        return $this->angleDifference;
    }
    
    public function getDistance(): float {
        return $this->distance;
    }
    
    public function getTimestamp(): float {
        return $this->timestamp;
    }
    
    private function calculateAngleDifference(): void {
        // Calculate the difference between player's facing direction and target direction
        $yawDiff = abs($this->normalizeAngle($this->playerYaw - $this->targetYaw));
        $pitchDiff = abs($this->playerPitch - $this->targetPitch);
        
        // Combined angle difference using 3D angle calculation
        $this->angleDifference = sqrt($yawDiff * $yawDiff + $pitchDiff * $pitchDiff);
    }
    
    private function normalizeAngle(float $angle): float {
        while ($angle > 180) $angle -= 360;
        while ($angle < -180) $angle += 360;
        return $angle;
    }
    
    public function parse(): string {
        // Pack: timestamp(8), facingTarget(1), playerYaw(4), playerPitch(4), targetYaw(4), targetPitch(4), distance(4), angleDiff(4)
        return pack('dCfffff', 
            $this->timestamp,
            $this->facingTarget ? 1 : 0,
            $this->playerYaw,
            $this->playerPitch,
            $this->targetYaw,
            $this->targetPitch,
            $this->distance,
            $this->angleDifference
        );
    }
    
    public function decode(string $bytes): string {
        $unpacked = unpack('dtimestamp/Cfacing/fplayerYaw/fplayerPitch/ftargetYaw/ftargetPitch/fdistance/fangleDiff', $bytes);
        if ($unpacked === false) {
            throw new \InvalidArgumentException("Invalid facing delta bytes");
        }
        
        $this->timestamp = $unpacked['timestamp'];
        $this->facingTarget = $unpacked['facing'] === 1;
        $this->playerYaw = $unpacked['playerYaw'];
        $this->playerPitch = $unpacked['playerPitch'];
        $this->targetYaw = $unpacked['targetYaw'];
        $this->targetPitch = $unpacked['targetPitch'];
        $this->distance = $unpacked['distance'];
        $this->angleDifference = $unpacked['angleDiff'];
        
        return "FacingDelta(facing={$this->facingTarget}, angleDiff={$this->angleDifference}°, distance={$this->distance})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::ATTACK;
    }
    
    public function getYawDifference(): float {
        return abs($this->normalizeAngle($this->playerYaw - $this->targetYaw));
    }
    
    public function getPitchDifference(): float {
        return abs($this->playerPitch - $this->targetPitch);
    }
    
    public function isWithinReasonableRange(): bool {
        // Check if the angle difference is within reasonable human limits
        return $this->angleDifference <= 45.0; // 45 degrees tolerance
    }
    
    public function isPerfectAim(): bool {
        // Suspiciously perfect aim (less than 1 degree difference)
        return $this->angleDifference < 1.0 && $this->distance > 3.0;
    }
    
    public function isSnapAim(): bool {
        // Large angle correction in short time (would need previous facing data)
        // This is a simplified check - in practice you'd compare with previous FacingDelta
        return $this->angleDifference > 90.0;
    }
    
    public function getAccuracyScore(): float {
        // Higher score = more accurate (0.0 to 1.0)
        $maxAngle = 180.0; // Maximum possible angle difference
        return max(0.0, 1.0 - ($this->angleDifference / $maxAngle));
    }
    
    public function getFacingQuality(): string {
        if ($this->isPerfectAim()) return "PERFECT";
        if ($this->angleDifference < 5.0) return "EXCELLENT";
        if ($this->angleDifference < 15.0) return "GOOD";
        if ($this->angleDifference < 45.0) return "FAIR";
        return "POOR";
    }
    
    public function isLikelyAimbot(): bool {
        // Combine multiple factors to detect aimbot
        $perfectAim = $this->isPerfectAim();
        $highAccuracy = $this->angleDifference < 2.0;
        $longDistance = $this->distance > 5.0;
        
        return $perfectAim || ($highAccuracy && $longDistance);
    }
    
    public function getDistanceAccuracyRatio(): float {
        // Ratio of accuracy to distance - higher values are more suspicious
        if ($this->distance <= 0) return 0.0;
        
        $accuracy = $this->getAccuracyScore();
        return $accuracy / $this->distance;
    }
    
    public function toArray(): array {
        return [
            'timestamp' => $this->timestamp,
            'facingTarget' => $this->facingTarget,
            'playerYaw' => $this->playerYaw,
            'playerPitch' => $this->playerPitch,
            'targetYaw' => $this->targetYaw,
            'targetPitch' => $this->targetPitch,
            'distance' => $this->distance,
            'angleDifference' => $this->angleDifference,
            'yawDifference' => $this->getYawDifference(),
            'pitchDifference' => $this->getPitchDifference(),
            'analysis' => [
                'quality' => $this->getFacingQuality(),
                'accuracyScore' => $this->getAccuracyScore(),
                'perfectAim' => $this->isPerfectAim(),
                'likelyAimbot' => $this->isLikelyAimbot(),
                'distanceAccuracyRatio' => $this->getDistanceAccuracyRatio(),
                'withinRange' => $this->isWithinReasonableRange(),
            ]
        ];
    }
}
