<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

enum EntityType: int {
    case PLAYER = 0;
    case MOB = 1;
    case TEXT = 2;
    case UNKNOWN = 3;
}

class EntityInfo {
    public int $entityId;
    public EntityType $type;
    public float $x;
    public float $y;
    public float $z;
    public float $yaw;
    public float $pitch;
    public int $health;
    public float $maxHealth;
    public bool $isAlive;
    
    public function __construct(
        int $entityId,
        EntityType $type,
        float $x, float $y, float $z,
        float $yaw, float $pitch,
        int $health, float $maxHealth = 100.0,
        bool $isAlive = true
    ) {
        $this->entityId = $entityId;
        $this->type = $type;
        $this->x = $x;
        $this->y = $y;
        $this->z = $z;
        $this->yaw = $yaw;
        $this->pitch = $pitch;
        $this->health = $health;
        $this->maxHealth = $maxHealth;
        $this->isAlive = $isAlive;
    }
    
    public function getHealthPercentage(): float {
        return $this->maxHealth > 0 ? ($this->health / $this->maxHealth) * 100.0 : 0.0;
    }
    
    public function getPosition(): array {
        return ['x' => $this->x, 'y' => $this->y, 'z' => $this->z];
    }
    
    public function getRotation(): array {
        return ['yaw' => $this->yaw, 'pitch' => $this->pitch];
    }
    
    public function distanceTo(EntityInfo $other): float {
        $dx = $this->x - $other->x;
        $dy = $this->y - $other->y;
        $dz = $this->z - $other->z;
        return sqrt($dx * $dx + $dy * $dy + $dz * $dz);
    }
}

class AttackEntityDelta extends ContextDelta {
    
    private EntityInfo $target;
    private EntityInfo $attacker;
    private float $distance;
    private float $damage;
    private bool $critical;
    private bool $blocked;
    private float $knockback;
    private float $timestamp;
    
    public function __construct(
        EntityInfo $target,
        EntityInfo $attacker,
        float $damage,
        bool $critical = false,
        bool $blocked = false,
        float $knockback = 0.0,
        float $timestamp = null
    ) {
        $this->target = $target;
        $this->attacker = $attacker;
        $this->damage = $damage;
        $this->critical = $critical;
        $this->blocked = $blocked;
        $this->knockback = $knockback;
        $this->timestamp = $timestamp ?? microtime(true);
        $this->distance = $attacker->distanceTo($target);
    }
    
    public function getTarget(): EntityInfo {
        return $this->target;
    }
    
    public function getAttacker(): EntityInfo {
        return $this->attacker;
    }
    
    public function getDistance(): float {
        return $this->distance;
    }
    
    public function getDamage(): float {
        return $this->damage;
    }
    
    public function isCritical(): bool {
        return $this->critical;
    }
    
    public function isBlocked(): bool {
        return $this->blocked;
    }
    
    public function getKnockback(): float {
        return $this->knockback;
    }
    
    public function getTimestamp(): float {
        return $this->timestamp;
    }
    
    public function parse(): string {
        // Pack entity data efficiently
        $data = pack('d', $this->timestamp); // 8 bytes
        
        // Attacker data (25 bytes)
        $data .= pack('VCfffffCfC', 
            $this->attacker->entityId,
            $this->attacker->type->value,
            $this->attacker->x,
            $this->attacker->y,
            $this->attacker->z,
            $this->attacker->yaw,
            $this->attacker->pitch,
            $this->attacker->health,
            $this->attacker->maxHealth,
            $this->attacker->isAlive ? 1 : 0
        );
        
        // Target data (25 bytes)
        $data .= pack('VCfffffCfC',
            $this->target->entityId,
            $this->target->type->value,
            $this->target->x,
            $this->target->y,
            $this->target->z,
            $this->target->yaw,
            $this->target->pitch,
            $this->target->health,
            $this->target->maxHealth,
            $this->target->isAlive ? 1 : 0
        );
        
        // Attack data (13 bytes)
        $flags = 0;
        if ($this->critical) $flags |= 0x01;
        if ($this->blocked) $flags |= 0x02;
        
        $data .= pack('fffC', $this->distance, $this->damage, $this->knockback, $flags);
        
        return $data;
    }
    
    public function decode(string $bytes): string {
        $offset = 0;
        
        // Decode timestamp
        $this->timestamp = unpack('d', substr($bytes, $offset, 8))[1];
        $offset += 8;
        
        // Decode attacker
        $attackerData = unpack('VentityId/Ctype/fx/fy/fz/fyaw/fpitch/Chealth/fmaxHealth/Calive', substr($bytes, $offset, 25));
        $this->attacker = new EntityInfo(
            $attackerData['entityId'],
            EntityType::from($attackerData['type']),
            $attackerData['x'], $attackerData['y'], $attackerData['z'],
            $attackerData['yaw'], $attackerData['pitch'],
            $attackerData['health'], $attackerData['maxHealth'],
            $attackerData['alive'] === 1
        );
        $offset += 25;
        
        // Decode target
        $targetData = unpack('VentityId/Ctype/fx/fy/fz/fyaw/fpitch/Chealth/fmaxHealth/Calive', substr($bytes, $offset, 25));
        $this->target = new EntityInfo(
            $targetData['entityId'],
            EntityType::from($targetData['type']),
            $targetData['x'], $targetData['y'], $targetData['z'],
            $targetData['yaw'], $targetData['pitch'],
            $targetData['health'], $targetData['maxHealth'],
            $targetData['alive'] === 1
        );
        $offset += 25;
        
        // Decode attack data
        $attackData = unpack('fdistance/fdamage/fknockback/Cflags', substr($bytes, $offset, 13));
        $this->distance = $attackData['distance'];
        $this->damage = $attackData['damage'];
        $this->knockback = $attackData['knockback'];
        $flags = $attackData['flags'];
        $this->critical = ($flags & 0x01) !== 0;
        $this->blocked = ($flags & 0x02) !== 0;
        
        return "AttackEntityDelta(attacker={$this->attacker->entityId}, target={$this->target->entityId}, damage={$this->damage}, distance={$this->distance})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::ATTACK;
    }
    
    public function isLongRangeAttack(): bool {
        return $this->distance > 6.0; // Suspicious if attacking from more than 6 blocks
    }
    
    public function isThroughWalls(): bool {
        // This would require block collision detection - simplified check
        // In practice, you'd check if there are blocks between attacker and target
        return false; // Placeholder - implement with world collision detection
    }
    
    public function isTargetBehindAttacker(): bool {
        // Calculate if target is behind the attacker based on yaw
        $attackerToTarget = atan2($this->target->z - $this->attacker->z, $this->target->x - $this->attacker->x);
        $attackerYawRad = deg2rad($this->attacker->yaw);
        
        $angleDiff = abs($attackerToTarget - $attackerYawRad);
        if ($angleDiff > M_PI) $angleDiff = 2 * M_PI - $angleDiff;
        
        return $angleDiff > M_PI / 2; // More than 90 degrees difference
    }
    
    public function getAttackAngle(): float {
        // Calculate the angle between attacker's facing direction and target
        $attackerToTarget = atan2($this->target->z - $this->attacker->z, $this->target->x - $this->attacker->x);
        $attackerYawRad = deg2rad($this->attacker->yaw);
        
        $angleDiff = abs($attackerToTarget - $attackerYawRad);
        if ($angleDiff > M_PI) $angleDiff = 2 * M_PI - $angleDiff;
        
        return rad2deg($angleDiff);
    }
    
    public function isReasonableAttack(): bool {
        return !$this->isLongRangeAttack() && 
               !$this->isTargetBehindAttacker() && 
               $this->getAttackAngle() < 45.0;
    }
    
    public function getSuspicionScore(): float {
        $suspicion = 0.0;
        
        if ($this->isLongRangeAttack()) $suspicion += 0.4;
        if ($this->isTargetBehindAttacker()) $suspicion += 0.3;
        if ($this->getAttackAngle() > 90.0) $suspicion += 0.3;
        if ($this->isThroughWalls()) $suspicion += 0.8;
        
        return min(1.0, $suspicion);
    }
    
    public function toArray(): array {
        return [
            'timestamp' => $this->timestamp,
            'attacker' => [
                'entityId' => $this->attacker->entityId,
                'type' => $this->attacker->type->name,
                'position' => $this->attacker->getPosition(),
                'rotation' => $this->attacker->getRotation(),
                'health' => $this->attacker->health,
                'healthPercentage' => $this->attacker->getHealthPercentage(),
                'isAlive' => $this->attacker->isAlive,
            ],
            'target' => [
                'entityId' => $this->target->entityId,
                'type' => $this->target->type->name,
                'position' => $this->target->getPosition(),
                'rotation' => $this->target->getRotation(),
                'health' => $this->target->health,
                'healthPercentage' => $this->target->getHealthPercentage(),
                'isAlive' => $this->target->isAlive,
            ],
            'attack' => [
                'distance' => $this->distance,
                'damage' => $this->damage,
                'critical' => $this->critical,
                'blocked' => $this->blocked,
                'knockback' => $this->knockback,
                'angle' => $this->getAttackAngle(),
            ],
            'analysis' => [
                'longRange' => $this->isLongRangeAttack(),
                'targetBehind' => $this->isTargetBehindAttacker(),
                'throughWalls' => $this->isThroughWalls(),
                'reasonable' => $this->isReasonableAttack(),
                'suspicionScore' => $this->getSuspicionScore(),
            ]
        ];
    }
}
