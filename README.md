# EITHES
Eyes In THE Sky

The most powerful Minecraft Bedrock AntiCheat

- []

## How it works (From a functional level)

Different Approaches:

- Random Forests & Gradient Boosting (For Stats, Interactions)
- Recurrent Neural Networks (RNN) (For movement, attacks, interactions)
- Long Short Term Memory (LSTM) (^ same as above)
- Isolation Forest and One Class SVM (Good for detecting anomalies)
- Convolutional Neural Networks (CNN) used for movement over 3d space and xray

### Basic understanding

In order to accomplish a task like this you first need to understand how each of these different submodels will interact with each-other to give you the most optimal result, in this section we will go over:

- Structuring Data
- Buffer Classification
- Arbitration

#### Structuring Data

This is a big task, with LOTS of data, it is ABSOLUTELY VITAL that you store as much meaningful information as possible while discarding the garbage. We do this in our approach to saving our data prior to training.

A packet in most online Multiplayer games comes in the form of a payload or event, take the following graph as example, where we use Deltas to push to our context buffer:

```mermaid
zenuml
    Client.Movement {
      Server."MovePacket { x, y, z}"
      Server->Models: Push Delta: currx - x, curry - y, currz -z
      Models->Server: Confidence: PENDING
      Server->Client: Allow Movement
      return "Allow"
    }
    
    Client.Click {
    	Server.InteractionPacket
    	Server->Models: Push Delta: Click + 1 
    	Models->Server: Confidence: PENDING
    	Server->Client: ACK
    	return "void"
    }
    
    Server.Tick {
    	Server->Models: Confidence Pull
    	Models->Server: Player: { movement: 0,  autoclick: 0.93}
    	
    	return "Banned: Autoclicking: Confidence > .9"
    }

```


In the above example imagine that our delta looked like the following:
```rust
pub enum ContextDelta {
    MoveUpdate { x: f32, y: f32, z: f32 },
    PlayerCps,
    AirTimeUpdate { ticks: usize },
    CameraUpdate { yaw: f32, pitch: f32 },
    PlayerJump,
    CollisionUpdate { offset: usize, colliding: bool },
}

pub struct AttackContext {
    other_pos: Position,
    duration: usize,
    collision_matrix: Matrix [ 0, 0, 1, 1, 1, 0 ];
}

pub struct PlayerContext {
    position: Position,
    cps: u16,
    camera: Camera,
    attack_ctx: Option<AttackContext>,
    move_ctx: Option<MovementContext>,
    collision: Matrix,
    network_stack_latency: usize,
    threshold: u16
}

pub struct MovementContext {
    velocity: f32,
    direction: Vector3,
    air_time: usize,
    attack_velocity: Vector3,    
}

pub struct Position {
    x: f32,
    y: f32,
    z: f32,
}

pub struct Camera {
    yaw: f32,
    pitch: f32,
}

pub struct Vector3 {
    x: f32,
    y: f32,
    z: f32,
}

pub struct Matrix {
    values: [[i32; 3]; 2], // A simple 2x3 matrix for the collision model
}

```

In the above set of data, we are structuring our data in such a way that each model will be able to properly understand the player context overtime while lowering our data footprint.

The above approach will require us to save the entire `PlayerContext` and feed it into our RNN or LTSM models for proper prediction over time. These models will be able to detect things we really can't even code checks for accurately like whether or not a player is using aimbot or artificially clicking at times.

The data above would be flattened and 

### Arbitration

This is a logical layer that determines what the executable action should be from all the models, if we get something like:
```json
{
    "flight": .43,
    "auto_click": .23,
    "speed": .96,
    "velocity": .96
}
```

The arbitration layer can choose the outlying cheat at will if two confidence levels are the same, here for example, let's say our velocity model is less reliable then our speed models, we might have logic that looks like:
```python
if (abs(levels.velocity - levels.speed) > THRESHOLD_DIFF) && levels.velocity > levels.speed:
    # Prefer speed
    return levels.speed
else 
	return HIGHEST_CONFIDENCE
```

Obviously this is just pseudo logic, but it gets the point across that sometimes you can take confidence levels from other domains and use them to enforce or prefer another one, like you could very well add logic like:

```python
if (levels.speed > 0.5) && (levels.velocity > 0.6 && levels.velocity < 0.8):
    levels.speed += MODIFER_SPEED_INC # They're probably speeding if the velocity model is less than .8 but bigger than .6
```

## How it works (From a low level [CODE])
We have the following modules inside of the `eithes` crate.
- `context`
- `normalization`
- `models`
- `ml`

### Context
Context houses the code to shape the data into the what the models require and for how our data is saved and loaded.

The primary objective for this module is to provide a structure for the data, and allow each field to be recorded in the form of a `ContextDelta`.
This means a player will start with a `PlayerContext` and then we will push deltas to the context buffer, which will then be used to train the models.


### Normalization
Normalization is the process of scaling and centering the data so that it fits within a certain range, this is important for the models to be able to properly understand the data.

This means things like `player.position` will be normalized to a range from 2 bytes, (1000 block threshold though) meaning, we normalize the distance between the player and another player to a range of 0-1000.
This is important for the models to be able to properly understand the data, as scaling can be a big issue when it comes to training.

Other things like `player.position` and `target.position` will be normalized to the distance, so `player.position - target.position`. Other optimizations will be made, like normalizing the `player.velocity` to a range of 0-1, and so on.

The primary objective for this module is to convert the data into a format that the models can understand, and to make sure that it is as accurate as possible.

### Models
This layer is an abstraction

### ML
This is the crate the the proxy will be using, it has the following modules:






## Approach

- Maybe we should use a giant model with different layers and feed it into our LTSM, but how do we format the feature (input) data?
- WE could use a convolutional network and just generate a heat map that might be a better idea
- generally how this works is we have a bunch of different m