<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class PositionDelta extends ContextDelta {
    
    private float $x;
    private float $y;
    private float $z;
    private float $lastX;
    private float $lastY;
    private float $lastZ;
    
    public function __construct(float $x, float $y, float $z, float $lastX, float $lastY, float $lastZ) {
        $this->x = $x;
        $this->y = $y;
        $this->z = $z;
        $this->lastX = $lastX;
        $this->lastY = $lastY;
        $this->lastZ = $lastZ;
    }
    
    public function getX(): float {
        return $this->x;
    }
    
    public function getY(): float {
        return $this->y;
    }
    
    public function getZ(): float {
        return $this->z;
    }
    
    public function getLastX(): float {
        return $this->lastX;
    }
    
    public function getLastY(): float {
        return $this->lastY;
    }
    
    public function getLastZ(): float {
        return $this->lastZ;
    }
    
    public function getDeltaX(): float {
        return $this->x - $this->lastX;
    }
    
    public function getDeltaY(): float {
        return $this->y - $this->lastY;
    }
    
    public function getDeltaZ(): float {
        return $this->z - $this->lastZ;
    }
    
    public function parse(): string {
        // Pack as: 6 floats (current x,y,z then last x,y,z)
        return pack('ffffff', $this->x, $this->y, $this->z, $this->lastX, $this->lastY, $this->lastZ);
    }
    
    public function decode(string $bytes): string {
        $unpacked = unpack('fx/fy/fz/flastX/flastY/flastZ', $bytes);
        if ($unpacked === false) {
            throw new \InvalidArgumentException("Invalid position delta bytes");
        }
        
        $this->x = $unpacked['x'];
        $this->y = $unpacked['y'];
        $this->z = $unpacked['z'];
        $this->lastX = $unpacked['lastX'];
        $this->lastY = $unpacked['lastY'];
        $this->lastZ = $unpacked['lastZ'];
        
        $deltaX = $this->getDeltaX();
        $deltaY = $this->getDeltaY();
        $deltaZ = $this->getDeltaZ();
        
        return "PositionDelta(current=({$this->x}, {$this->y}, {$this->z}), delta=({$deltaX}, {$deltaY}, {$deltaZ}))";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::MOVE;
    }
    
    public function getDistance(): float {
        $dx = $this->getDeltaX();
        $dy = $this->getDeltaY();
        $dz = $this->getDeltaZ();
        return sqrt($dx * $dx + $dy * $dy + $dz * $dz);
    }
    
    public function getHorizontalDistance(): float {
        $dx = $this->getDeltaX();
        $dz = $this->getDeltaZ();
        return sqrt($dx * $dx + $dz * $dz);
    }
    
    public function hasMoved(): bool {
        return $this->getDistance() > 0.001;
    }
}
