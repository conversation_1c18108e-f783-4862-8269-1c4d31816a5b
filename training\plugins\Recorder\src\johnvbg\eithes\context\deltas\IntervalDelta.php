<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class IntervalDelta extends ContextDelta {
    
    private int $intervalTicks;
    private float $intervalMs;
    private int $totalAttacks;
    private float $averageCps;
    private array $recentIntervals; // Last 10 intervals for CPS calculation
    private float $timestamp;
    
    public function __construct(
        int $intervalTicks, 
        float $intervalMs, 
        int $totalAttacks = 1,
        array $recentIntervals = [],
        float $timestamp = null
    ) {
        $this->intervalTicks = $intervalTicks;
        $this->intervalMs = $intervalMs;
        $this->totalAttacks = $totalAttacks;
        $this->recentIntervals = $recentIntervals;
        $this->timestamp = $timestamp ?? microtime(true);
        $this->calculateAverageCps();
    }
    
    public function getIntervalTicks(): int {
        return $this->intervalTicks;
    }
    
    public function getIntervalMs(): float {
        return $this->intervalMs;
    }
    
    public function getTotalAttacks(): int {
        return $this->totalAttacks;
    }
    
    public function getAverageCps(): float {
        return $this->averageCps;
    }
    
    public function getRecentIntervals(): array {
        return $this->recentIntervals;
    }
    
    public function getTimestamp(): float {
        return $this->timestamp;
    }
    
    public function addInterval(float $intervalMs): void {
        $this->recentIntervals[] = $intervalMs;
        
        // Keep only last 10 intervals
        if (count($this->recentIntervals) > 10) {
            array_shift($this->recentIntervals);
        }
        
        $this->totalAttacks++;
        $this->calculateAverageCps();
    }
    
    private function calculateAverageCps(): void {
        if (empty($this->recentIntervals)) {
            $this->averageCps = 0.0;
            return;
        }
        
        $totalMs = array_sum($this->recentIntervals);
        $avgInterval = $totalMs / count($this->recentIntervals);
        
        // CPS = 1000ms / average interval
        $this->averageCps = $avgInterval > 0 ? 1000.0 / $avgInterval : 0.0;
    }
    
    public function parse(): string {
        // Pack: timestamp(8), intervalTicks(4), intervalMs(4), totalAttacks(4), avgCps(4), intervalCount(1), intervals(4*count)
        $data = pack('dVffC', 
            $this->timestamp,
            $this->intervalTicks, 
            $this->intervalMs, 
            $this->averageCps,
            count($this->recentIntervals)
        );
        
        // Pack recent intervals
        foreach ($this->recentIntervals as $interval) {
            $data .= pack('f', $interval);
        }
        
        return $data;
    }
    
    public function decode(string $bytes): string {
        $offset = 0;
        
        // Unpack main data
        $mainData = unpack('dtimestamp/VintervalTicks/fintervalMs/favgCps/CintervalCount', substr($bytes, 0, 21));
        if ($mainData === false) {
            throw new \InvalidArgumentException("Invalid interval delta bytes");
        }
        
        $this->timestamp = $mainData['timestamp'];
        $this->intervalTicks = $mainData['intervalTicks'];
        $this->intervalMs = $mainData['intervalMs'];
        $this->averageCps = $mainData['avgCps'];
        $intervalCount = $mainData['intervalCount'];
        
        $offset = 21;
        
        // Unpack intervals
        $this->recentIntervals = [];
        for ($i = 0; $i < $intervalCount; $i++) {
            $interval = unpack('f', substr($bytes, $offset, 4))[1];
            $this->recentIntervals[] = $interval;
            $offset += 4;
        }
        
        return "IntervalDelta(ticks={$this->intervalTicks}, ms={$this->intervalMs}, cps={$this->averageCps}, attacks={$this->totalAttacks})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::ATTACK;
    }
    
    public function getCurrentCps(): float {
        return $this->intervalMs > 0 ? 1000.0 / $this->intervalMs : 0.0;
    }
    
    public function isHighCps(): bool {
        return $this->averageCps > 15.0; // Suspicious if over 15 CPS
    }
    
    public function isInhumanCps(): bool {
        return $this->averageCps > 25.0; // Definitely inhuman if over 25 CPS
    }
    
    public function hasConsistentTiming(): bool {
        if (count($this->recentIntervals) < 3) return false;
        
        $mean = array_sum($this->recentIntervals) / count($this->recentIntervals);
        $variance = 0;
        
        foreach ($this->recentIntervals as $interval) {
            $variance += pow($interval - $mean, 2);
        }
        
        $variance /= count($this->recentIntervals);
        $stdDev = sqrt($variance);
        
        // If standard deviation is very low, timing is suspiciously consistent
        return $stdDev < 5.0 && $this->averageCps > 8.0;
    }
    
    public function getTimingPattern(): string {
        if ($this->isInhumanCps()) return "INHUMAN";
        if ($this->isHighCps() && $this->hasConsistentTiming()) return "AUTO_CLICK";
        if ($this->isHighCps()) return "HIGH_CPS";
        if ($this->hasConsistentTiming()) return "CONSISTENT";
        return "NORMAL";
    }
    
    public function getJitterLevel(): float {
        if (count($this->recentIntervals) < 2) return 0.0;
        
        $mean = array_sum($this->recentIntervals) / count($this->recentIntervals);
        $jitter = 0;
        
        foreach ($this->recentIntervals as $interval) {
            $jitter += abs($interval - $mean);
        }
        
        return $jitter / count($this->recentIntervals);
    }
    
    public function toArray(): array {
        return [
            'timestamp' => $this->timestamp,
            'intervalTicks' => $this->intervalTicks,
            'intervalMs' => $this->intervalMs,
            'totalAttacks' => $this->totalAttacks,
            'averageCps' => $this->averageCps,
            'currentCps' => $this->getCurrentCps(),
            'recentIntervals' => $this->recentIntervals,
            'analysis' => [
                'pattern' => $this->getTimingPattern(),
                'highCps' => $this->isHighCps(),
                'inhumanCps' => $this->isInhumanCps(),
                'consistent' => $this->hasConsistentTiming(),
                'jitterLevel' => $this->getJitterLevel(),
            ]
        ];
    }
}
