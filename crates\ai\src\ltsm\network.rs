use crate::prediction::Prediction;

use super::{
    cell::Ltsm<PERSON>ell,
    labels::{LabelFactory, LabeledData},
};
use ndarray::Array2;
use ndarray_rand::RandomExt;
use ndarray_rand::rand_distr::Normal;

// This follows a recurrent neural network
// because we're using labels we need to use a softmax output with cross entropy
pub struct Network {
    /// A cell is the powerhouse of the LTSM
    /// It's a stateful machine
    cells: Vec<LtsmCell>,
    label_factory: LabelFactory,
    /// Output layer weights
    output_weights: Array2<f64>,
    /// Output layer bias
    output_bias: Array2<f64>,
    /// Learning rate for training
    learning_rate: f64,
    /// Previous weight updates for momentum
    prev_output_weight_update: Array2<f64>,
    prev_output_bias_update: Array2<f64>,
}

impl Network {
    pub fn new(
        input_size: usize,
        hidden_size: usize,
        num_layers: usize,
        learning_rate: Option<f64>,
    ) -> Self {
        let mut cells = Vec::with_capacity(num_layers);
        let mut layer_input_size = input_size;

        for _ in 0..num_layers {
            cells.push(LtsmCell::new(layer_input_size, hidden_size));
            layer_input_size = hidden_size;
        }

        // Initialize with Xavier/Glorot initialization
        let weight_std = (2.0 / (hidden_size as f64)).sqrt();
        let distributions = Normal::new(0.0, weight_std).unwrap();
        let output_weights = Array2::random((3, hidden_size), distributions);
        let output_bias = Array2::zeros((3, 1));

        Self {
            cells,
            label_factory: LabelFactory::new(),
            output_weights,
            output_bias,
            learning_rate: learning_rate.unwrap_or(0.01), // Much lower learning rate
            prev_output_weight_update: Array2::zeros((3, hidden_size)),
            prev_output_bias_update: Array2::zeros((3, 1)),
        }
    }

    pub fn train(&mut self, data: &LabeledData) -> f64 {
        let label_id = self.label_factory.add_label(&data.label);
        let mut hidden_states =
            vec![Array2::zeros((self.cells[0].hidden_size, 1)); self.cells.len()];
        let mut cell_states = vec![Array2::zeros((self.cells[0].hidden_size, 1)); self.cells.len()];

        // Forward pass through all layers
        for (i, cell) in self.cells.iter().enumerate() {
            let input = if i == 0 {
                data.get_sequence()
            } else {
                &hidden_states[i - 1]
            };

            let states = cell.forward(input, &hidden_states[i], &cell_states[i]);
            hidden_states[i] = states.hidden;
            cell_states[i] = states.cell;
        }

        // Get final hidden state from last layer
        let final_hidden = &hidden_states[self.cells.len() - 1];

        // Compute output logits
        let logits = &self.output_weights.dot(final_hidden) + &self.output_bias;

        // Apply softmax activation
        let max_logit = logits.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let exp_logits = logits.map(|&x| (x - max_logit).exp());
        let sum_exp = exp_logits.sum();
        let output = exp_logits.map(|&x| x / sum_exp);

        // Compute cross entropy loss
        let epsilon = 1e-15;
        let mut target = Array2::zeros((3, 1));
        target[[label_id, 0]] = 1.0;
        let loss = -target
            .iter()
            .zip(output.iter())
            .map(|(&t, &o)| t * (o + epsilon).ln())
            .sum::<f64>();

        // Backward pass & layer with gradient clipping and momentum
        let grad_output = output.clone() - target;
        let grad_weights = grad_output.dot(&final_hidden.t());
        let grad_bias = grad_output.clone();
        let grad_weights = self.clip_gradient(&grad_weights, 0.1);
        let grad_bias = self.clip_gradient(&grad_bias, 0.1);
        let momentum = 0.5;
        let weight_update =
            &grad_weights * self.learning_rate + &self.prev_output_weight_update * momentum;
        let bias_update =
            &grad_bias * self.learning_rate + &self.prev_output_bias_update * momentum;

        self.output_weights = &self.output_weights - &weight_update;
        self.output_bias = &self.output_bias - &bias_update;
        self.prev_output_weight_update = weight_update;
        self.prev_output_bias_update = bias_update;

        // Backpropagate through LSTM layers
        let mut grad_hidden = self.output_weights.t().dot(&grad_output);
        let mut grad_cell = Array2::zeros((self.cells[0].hidden_size, 1));

        for i in (0..self.cells.len()).rev() {
            let input = if i == 0 {
                data.get_sequence()
            } else {
                &hidden_states[i - 1]
            };

            let (grad_w_ih, grad_w_hh, grad_b_ih, grad_b_hh, grad_input, grad_h) = self.cells[i]
                .backward(
                    input,
                    &hidden_states[i],
                    &cell_states[i],
                    &grad_hidden,
                    &grad_cell,
                );

            // Update LSTM cell gradient clipping
            let grad_w_ih = self.clip_gradient(&grad_w_ih, 0.1);
            let grad_w_hh = self.clip_gradient(&grad_w_hh, 0.1);
            let grad_b_ih = self.clip_gradient(&grad_b_ih, 0.1);
            let grad_b_hh = self.clip_gradient(&grad_b_hh, 0.1);

            self.cells[i].w_ih = &self.cells[i].w_ih - &(grad_w_ih * self.learning_rate);
            self.cells[i].w_hh = &self.cells[i].w_hh - &(grad_w_hh * self.learning_rate);
            self.cells[i].b_ih = &self.cells[i].b_ih - &(grad_b_ih * self.learning_rate);
            self.cells[i].b_hh = &self.cells[i].b_hh - &(grad_b_hh * self.learning_rate);

            grad_hidden = grad_h;
            grad_cell = grad_input;
        }

        loss
    }

    pub fn predict(&self, data: &Array2<f64>) -> Prediction {
        let mut hidden_states =
            vec![Array2::zeros((self.cells[0].hidden_size, 1)); self.cells.len()];
        let mut cell_states = vec![Array2::zeros((self.cells[0].hidden_size, 1)); self.cells.len()];

        // Forward pass through all layers
        for (i, cell) in self.cells.iter().enumerate() {
            let input = if i == 0 { data } else { &hidden_states[i - 1] };

            let states = cell.forward(input, &hidden_states[i], &cell_states[i]);
            hidden_states[i] = states.hidden;
            cell_states[i] = states.cell;
        }

        // Get final hidden state from last layer
        let final_hidden = &hidden_states[self.cells.len() - 1];
        let logits = &self.output_weights.dot(final_hidden) + &self.output_bias;

        // Apply softmax activation
        let max_logit = logits.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let exp_logits = logits.map(|&x| (x - max_logit).exp());
        let sum_exp = exp_logits.sum();
        let output = exp_logits.map(|&x| x / sum_exp);

        // Get predicted label (class with highest probability)
        let mut max_prob = 0.0;
        let mut max_idx = 0;
        for (i, &prob) in output.iter().enumerate() {
            if prob > max_prob {
                max_prob = prob;
                max_idx = i;
            }
        }

        let predicted_label = self
            .label_factory
            .get_label(&max_idx)
            .unwrap_or_else(|| "unknown".to_string());

        // Calculate confidence level (0 to 1)
        // Confidence is the probability of the predicted class
        let confidence = max_prob;

        Prediction::new(&predicted_label, output, confidence)
    }

    fn clip_gradient(&self, grad: &Array2<f64>, max_norm: f64) -> Array2<f64> {
        let norm = grad.iter().map(|&x| x * x).sum::<f64>().sqrt();
        if norm > max_norm {
            grad * (max_norm / norm)
        } else {
            grad.clone()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ndarray::arr2;

    #[test]
    fn test_network_initialization() {
        let input_size = 3;
        let hidden_size = 4;
        let num_layers = 2;
        let learning_rate = Some(0.01);

        let network = Network::new(input_size, hidden_size, num_layers, learning_rate);

        assert_eq!(network.cells.len(), num_layers);
        assert_eq!(network.cells[0].hidden_size, hidden_size);
        assert_eq!(network.output_weights.shape(), &[3, hidden_size]);
        assert_eq!(network.output_bias.shape(), &[3, 1]);
    }

    #[test]
    fn test_simple_training() {
        let mut network = Network::new(2, 16, 2, Some(0.1));

        // Create simple training data
        let input = arr2(&[[0.1], [0.1]]);
        let data = LabeledData::new("class1", input, false);

        // Train the network multiple times
        let mut prev_loss = f64::INFINITY;
        for _ in 0..10 {
            // Multiple training iterations
            let loss = network.train(&data);
            assert!(loss.is_finite(), "Loss should be finite");
            assert!(loss <= prev_loss, "Loss should decrease or stay the same");
            prev_loss = loss;
        }
    }

    #[test]
    fn test_prediction() {
        let mut network = Network::new(2, 16, 2, Some(0.1)); // Increased hidden size and layers

        // Create and train on some data
        let input = arr2(&[[0.1], [0.1]]);
        let data = LabeledData::new("class1", input.clone(), false);

        // Train multiple times
        for _ in 0..10 {
            network.train(&data);
        }

        // Make prediction
        let prediction = network.predict(&input);

        // Check prediction outputs
        assert!(
            !prediction.get_label().is_empty(),
            "Predicted label should not be empty"
        );
        assert_eq!(prediction.get_output().shape(), &[3, 1]);
        assert!(
            prediction.get_output()[[0, 0]] >= 0.0 && prediction.get_output()[[0, 0]] <= 1.0,
            "Probability should be between 0 and 1"
        );
        assert!(
            prediction.get_confidence().get_score() >= 0.0
                && prediction.get_confidence().get_score() <= 1.0,
            "Confidence should be between 0 and 1"
        );
    }

    #[test]
    fn test_multiple_labels() {
        let mut network = Network::new(2, 32, 2, Some(0.5));

        let input1 = arr2(&[[-1.0], [-1.0]]);
        let data1 = LabeledData::new("cheat.killaura", input1.clone(), false);

        // Even more extreme values
        let input2 = arr2(&[[1.0], [1.0]]);
        let data2 = LabeledData::new("cheat.reach", input2.clone(), false);

        let mut prev_loss = f64::INFINITY;
        for i in 0..100 {
            // More training iterations
            let loss1 = network.train(&data1);
            let loss2 = network.train(&data2);
            let total_loss = loss1 + loss2;

            if i % 10 == 0 {
                println!("Iteration {} - Loss: {}", i, total_loss);
            }

            assert!(total_loss.is_finite(), "Loss should be finite");
            if i > 0 {
                assert!(
                    total_loss <= prev_loss * 1.1,
                    "Loss should generally decrease (allowing for small fluctuations)"
                );
            }
            prev_loss = total_loss;
        }

        let pred1 = network.predict(&input1);
        let pred2 = network.predict(&input2);

        println!("\nFinal Predictions:");
        println!(
            "Player::cheater_5 (cheat.kill_aura): {} (prob: {}, conf: {})",
            pred1.get_label(),
            pred1.get_output()[[0, 0]],
            pred1.get_confidence().get_score()
        );
        println!(
            "Player::cheater_3 (cheat.reach): {} (prob: {}, conf: {})",
            pred2.get_label(),
            pred2.get_output()[[0, 0]],
            pred2.get_confidence().get_score()
        );

        assert_ne!(
            pred1.get_label(),
            pred2.get_label(),
            "Different inputs should give different predictions"
        );
        assert!(
            (pred1.get_output()[[0, 0]] - pred2.get_output()[[0, 0]]).abs() > 0.5,
            "Probabilities should be significantly different"
        );

        // Test some intermediate values
        let input_mid = arr2(&[[0.0], [0.0]]);
        let pred_mid = network.predict(&input_mid);
        assert!(
            pred_mid.get_output()[[0, 0]] > 0.1 && pred_mid.get_output()[[0, 0]] < 0.9,
            "Middle input should give less confident prediction"
        );
        assert!(
            pred_mid.get_confidence().get_score() < 0.5,
            "Middle input should have low confidence"
        );
    }

    #[test]
    fn test_normalized_input() {
        let mut network = Network::new(2, 16, 2, Some(0.1)); // Increased hidden size and layers

        let input = arr2(&[[100.0], [200.0]]);
        let data = LabeledData::new("class1", input, true);

        let normalized_seq = data.get_sequence();
        for &val in normalized_seq.iter() {
            assert!(
                val >= 0.0 && val <= 1.0,
                "Normalized values should be between 0 and 1"
            );
        }

        for _ in 0..10 {
            network.train(&data);
        }
        let prediction = network.predict(data.get_sequence());
        assert!(prediction.get_output()[[0, 0]] >= 0.0 && prediction.get_output()[[0, 0]] <= 1.0);
    }

    #[test]
    fn test_multiple_layers() {
        let input_size = 2;
        let hidden_size = 16;
        let num_layers = 3;
        let mut network = Network::new(input_size, hidden_size, num_layers, Some(0.1));

        // Create training data
        let input = arr2(&[[0.1], [0.1]]);
        let data = LabeledData::new("class1", input.clone(), false);

        let mut prev_loss = f64::INFINITY;
        for _ in 0..10 {
            let loss = network.train(&data);
            assert!(loss.is_finite(), "Loss should be a finite number");
            assert!(loss <= prev_loss, "Loss should decrease or stay the same");
            prev_loss = loss;
        }

        let prediction = network.predict(&input);
        assert!(
            prediction.get_output()[[0, 0]].is_finite(),
            "Probability should be a finite number"
        );
    }

    #[test]
    fn test_edge_cases() {
        let mut network = Network::new(2, 16, 2, Some(0.1));

        let zero_input = arr2(&[[0.0], [0.0]]);
        let zero_data = LabeledData::new("zero", zero_input.clone(), false);

        let max_input = arr2(&[[1.0], [1.0]]);
        let max_data = LabeledData::new("max", max_input.clone(), false);

        for _ in 0..10 {
            network.train(&zero_data);
            network.train(&max_data);
        }

        let zero_pred = network.predict(&zero_input);
        let max_pred = network.predict(&max_input);

        assert!(zero_pred.get_output()[[0, 0]].is_finite());
        assert!(max_pred.get_output()[[0, 0]].is_finite());
    }
}
