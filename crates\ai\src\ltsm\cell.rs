use ndarray::{Array2, Axis, s};
use ndarray_rand::RandomExt;
use ndarray_rand::rand_distr::Uniform;

use crate::math::sigmoid::sigmoid;
use crate::math::tangent::tangent;

pub struct LtsmCell {
    /// Weight, input gate (input to hidden), this is sized as GATES * HIDDEN_SIZE * FEATURE_SIZE, dist
    pub w_ih: Array2<f64>,
    /// Weight, hiden to hidden connection
    pub w_hh: Array2<f64>,
    /// Bias, input to hidden
    pub b_ih: Array2<f64>,
    /// Bias hidden to hidden
    pub b_hh: Array2<f64>,
    /// This is essentially the amount of hidden states or nuerons of this cell.
    pub hidden_size: usize,
}

impl LtsmCell {
    /// The input size should be the amount of features. While hidden size is
    /// the amount of hidden states to connect these features.
    pub fn new(input_size: usize, hidden_size: usize) -> Self {
        let distributions = Uniform::new(-0.1, 0.1);
        // we have four gates: input, forget, cell, output
        let gates: usize = 4;
        Self {
            w_ih: Array2::random((gates * hidden_size, input_size), distributions),
            w_hh: Array2::random((gates * hidden_size, hidden_size), distributions),
            b_ih: Array2::zeros((4 * hidden_size, 1)),
            b_hh: Array2::zeros((4 * hidden_size, 1)),
            hidden_size,
        }
    }

    /// This is essentially the train function, the only difference here is it is
    /// passing the cell forward, this is advancing the cell forwards trying to predict the new
    /// cell state.
    pub fn forward(
        &self,
        input: &Array2<f64>,
        hidden_state: &Array2<f64>,
        cell_state: &Array2<f64>,
    ) -> CellStates {
        let gates = &self.w_ih.dot(input) + &self.b_ih + &self.w_hh.dot(hidden_state) + &self.b_hh;

        let input_gate = gates
            .slice(s![0..self.hidden_size, ..])
            .map(|&x| sigmoid(x));
        let forget_gate = gates
            .slice(s![self.hidden_size..2 * self.hidden_size, ..])
            .map(|&x| sigmoid(x));
        let cell_gate = gates
            .slice(s![2 * self.hidden_size..3 * self.hidden_size, ..])
            .map(|&x| tangent(x));
        let output_gate = gates
            .slice(s![3 * self.hidden_size..4 * self.hidden_size, ..])
            .map(|&x| sigmoid(x));

        let new_cell_state = &forget_gate * cell_state + &input_gate * &cell_gate;
        let new_hidden_state = &output_gate * new_cell_state.map(|&x| x.tanh());

        CellStates::new(new_hidden_state, new_cell_state)
    }

    pub fn backward(
        &self,
        input: &Array2<f64>,
        hidden_state: &Array2<f64>,
        cell_state: &Array2<f64>,
        grad_output: &Array2<f64>,
        grad_cell: &Array2<f64>,
    ) -> (
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
        Array2<f64>,
    ) {
        // Forward pass to get gate values
        let gates = &self.w_ih.dot(input) + &self.b_ih + &self.w_hh.dot(hidden_state) + &self.b_hh;

        let input_gate = gates
            .slice(s![0..self.hidden_size, ..])
            .map(|&x| sigmoid(x));
        let forget_gate = gates
            .slice(s![self.hidden_size..2 * self.hidden_size, ..])
            .map(|&x| sigmoid(x));
        let cell_gate = gates
            .slice(s![2 * self.hidden_size..3 * self.hidden_size, ..])
            .map(|&x| tangent(x));
        let output_gate = gates
            .slice(s![3 * self.hidden_size..4 * self.hidden_size, ..])
            .map(|&x| sigmoid(x));

        let new_cell_state = &forget_gate * cell_state + &input_gate * &cell_gate;
        let tanh_cell = new_cell_state.map(|&x| x.tanh());

        // Compute gradients
        let grad_output_gate = grad_output * &tanh_cell;
        let grad_tanh_cell = grad_output * &output_gate;

        // Apply tanh derivative to cell state
        let tanh_derivative = 1.0 - &tanh_cell.map(|&x| x.powi(2));
        let grad_cell_state = &grad_tanh_cell * &tanh_derivative + grad_cell;

        // Compute gate gradients
        let grad_forget_gate = &grad_cell_state * cell_state;
        let grad_input_gate = &grad_cell_state * &cell_gate;
        let grad_cell_gate = &grad_cell_state * &input_gate;

        // Compute gate gradients
        let mut grad_gates = Array2::zeros((4 * self.hidden_size, 1));

        // Compute gradients for each gate with proper broadcasting
        let input_gate_grad = &grad_input_gate * &input_gate * (1.0 - &input_gate);
        let forget_gate_grad = &grad_forget_gate * &forget_gate * (1.0 - &forget_gate);
        let cell_gate_grad = &grad_cell_gate * (1.0 - &cell_gate.map(|&x| x.powi(2)));
        let output_gate_grad = &grad_output_gate * &output_gate * (1.0 - &output_gate);

        // Assign gradients to the appropriate slices
        grad_gates
            .slice_mut(s![0..self.hidden_size, ..])
            .assign(&input_gate_grad);
        grad_gates
            .slice_mut(s![self.hidden_size..2 * self.hidden_size, ..])
            .assign(&forget_gate_grad);
        grad_gates
            .slice_mut(s![2 * self.hidden_size..3 * self.hidden_size, ..])
            .assign(&cell_gate_grad);
        grad_gates
            .slice_mut(s![3 * self.hidden_size..4 * self.hidden_size, ..])
            .assign(&output_gate_grad);

        // Compute parameter gradients
        let grad_w_ih = grad_gates.dot(&input.t());
        let grad_w_hh = grad_gates.dot(&hidden_state.t());
        let grad_b_ih = grad_gates.sum_axis(Axis(1)).insert_axis(Axis(1));
        let grad_b_hh = grad_gates.sum_axis(Axis(1)).insert_axis(Axis(1));

        // Compute input gradients
        let grad_input = self.w_ih.t().dot(&grad_gates);
        let grad_hidden = self.w_hh.t().dot(&grad_gates);

        (
            grad_w_ih,
            grad_w_hh,
            grad_b_ih,
            grad_b_hh,
            grad_input,
            grad_hidden,
        )
    }
}

pub struct CellStates {
    pub(crate) hidden: Array2<f64>,
    pub(crate) cell: Array2<f64>,
}

impl CellStates {
    pub fn new(hidden: Array2<f64>, cell: Array2<f64>) -> Self {
        Self { hidden, cell }
    }
}

#[cfg(test)]
mod tests {
    use super::LtsmCell;
    use ndarray::arr2;

    #[test]
    fn test_forward() {
        let hidden_size = 2;
        let input_size = 3;

        let cell = LtsmCell::new(input_size, hidden_size);

        // Test input
        let input = arr2(&[[0.1], [0.2], [0.3]]);
        let hidden = arr2(&[[0.1], [0.2]]);
        let cell_state = arr2(&[[0.0], [0.0]]);

        let new_cell_state = cell.forward(&input, &hidden, &cell_state);

        // Check output shapes
        assert_eq!(new_cell_state.hidden.shape(), &[hidden_size, 1]);
        assert_eq!(new_cell_state.cell.shape(), &[hidden_size, 1]);

        // Values should be between -1 and 1 due to tanh activation
        for &val in new_cell_state.hidden.iter() {
            assert!(val >= -1.0 && val <= 1.0);
        }
    }

    #[test]
    fn test_backward() {
        let hidden_size = 2;
        let input_size = 3;

        let cell = LtsmCell::new(input_size, hidden_size);

        // Test inputs
        let input = arr2(&[[0.1], [0.2], [0.3]]);
        let hidden = arr2(&[[0.1], [0.2]]);
        let cell_state = arr2(&[[0.0], [0.0]]);
        let grad_output = arr2(&[[0.1], [0.2]]);
        let grad_cell = arr2(&[[0.0], [0.0]]);

        let (grad_w_ih, grad_w_hh, grad_b_ih, grad_b_hh, grad_input, grad_hidden) =
            cell.backward(&input, &hidden, &cell_state, &grad_output, &grad_cell);

        // Check gradient shapes
        assert_eq!(grad_w_ih.shape(), &[4 * hidden_size, input_size]);
        assert_eq!(grad_w_hh.shape(), &[4 * hidden_size, hidden_size]);
        assert_eq!(grad_b_ih.shape(), &[4 * hidden_size, 1]);
        assert_eq!(grad_b_hh.shape(), &[4 * hidden_size, 1]);
        assert_eq!(grad_input.shape(), &[input_size, 1]);
        assert_eq!(grad_hidden.shape(), &[hidden_size, 1]);
    }
}
