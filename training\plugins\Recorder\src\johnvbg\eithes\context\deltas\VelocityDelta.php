<?php

namespace johnvbg\eithes\context\deltas;

use johnvbg\eithes\context\ContextDelta;
use johnvbg\eithes\context\ContextDeltaId;

class VelocityDelta extends ContextDelta {
    
    private float $x;
    private float $y;
    private float $z;
    private int $ticks;
    
    public function __construct(float $x, float $y, float $z, int $ticks) {
        $this->x = $x;
        $this->y = $y;
        $this->z = $z;
        $this->ticks = $ticks;
    }
    
    public function getX(): float {
        return $this->x;
    }
    
    public function getY(): float {
        return $this->y;
    }
    
    public function getZ(): float {
        return $this->z;
    }
    
    public function getTicks(): int {
        return $this->ticks;
    }
    
    public function parse(): string {
        // Pack as: 4 bytes float x, 4 bytes float y, 4 bytes float z, 4 bytes int ticks
        return pack('fffV', $this->x, $this->y, $this->z, $this->ticks);
    }
    
    public function decode(string $bytes): string {
        $unpacked = unpack('fx/fy/fz/Vticks', $bytes);
        if ($unpacked === false) {
            throw new \InvalidArgumentException("Invalid velocity delta bytes");
        }
        
        $this->x = $unpacked['x'];
        $this->y = $unpacked['y'];
        $this->z = $unpacked['z'];
        $this->ticks = $unpacked['ticks'];
        
        return "VelocityDelta(x={$this->x}, y={$this->y}, z={$this->z}, ticks={$this->ticks})";
    }
    
    public static function getId(): ContextDeltaId {
        return ContextDeltaId::MOVE;
    }
    
    public function getMagnitude(): float {
        return sqrt($this->x * $this->x + $this->y * $this->y + $this->z * $this->z);
    }
    
    public function getHorizontalMagnitude(): float {
        return sqrt($this->x * $this->x + $this->z * $this->z);
    }
    
    public function isMoving(): bool {
        return $this->getMagnitude() > 0.001;
    }
}
