<?php

namespace johnvbg\eithes\context;

abstract class ContextDelta {
    /** Takes the current context and converts it to Bytes */
    public abstract function parse(): string;

    /** Takes the incoming byte information and attempts to decode it */
    public abstract function decode(string $bytes): string;

    /** Gets the id of this delta */
    public abstract static function getId(): ContextDeltaId;
}