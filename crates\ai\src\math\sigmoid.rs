/// Computes the sigmoid activation function
/// sigmoid(x) = 1 / (1 + e^(-x))
pub fn sigmoid(x: f64) -> f64 {
    1.0 / (1.0 + (-x).exp())
}

/// Computes the derivative of the sigmoid function
/// This is useful for gradiens
/// sigmoid'(x) = sigmoid(x) * (1 - sigmoid(x))
pub fn sigmoid_derivative(x: f64) -> f64 {
    let sig = sigmoid(x);
    sig * (1.0 - sig)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sigmoid() {
        assert!((sigmoid(0.0) - 0.5).abs() < 1e-10);
        assert!((sigmoid(100.0) - 1.0).abs() < 1e-10);
        assert!((sigmoid(-100.0)).abs() < 1e-10);
    }

    #[test]
    fn test_sigmoid_derivative() {
        assert!((sigmoid_derivative(0.0) - 0.25).abs() < 1e-10);
        assert!(sigmoid_derivative(100.0).abs() < 1e-10);
        assert!(sigmoid_derivative(-100.0).abs() < 1e-10);
    }
}
