use super::common::Vector3;
use binary_util::BinaryIo;

#[derive(<PERSON><PERSON>, <PERSON>bug, BinaryIo)]
#[repr(u8)]
pub enum MovementState {
    Walk,
    Sneak,
    Sprint,
}

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
#[repr(u8)]
pub enum MovePotion {
    Swiftness,
    Slowness,
    /// Blindness can cause slowness which is why it's added here
    Blindness,
    SlowFall,
    Levitation,
    Leaping,
    /// Posion can sometimes take effect during movement causing weird game behaviors
    Poison,
    /// Same as above, harming can cause weird movement sometimes.
    Harming,
    /// Occurs when the player kills a mob, good to know for Movement and attack models.
    WindCharge,
}

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
pub struct MovePotionState {
    pub potion: MovePotion,
    pub level: u8,
}

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
pub struct Movement {
    pub velocity: Velocity,
    pub position: Vector3,
    pub last_pos: Vector3,
    pub state: MovementState,
    pub potion: MovePotionState,
}

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
#[repr(u8)]
pub enum MovementDelta {
    Velocity(Velocity),
    Position(Vector3),
    State(MovementState),
    Potion(MovePotionState),
}

#[derive(<PERSON><PERSON>, Debug, BinaryIo)]
pub struct Velocity {
    /// The x velocity
    pub x: f32,
    pub y: f32,
    pub z: f32,
    /// The time of ticks this velocity was recorded in.
    pub ticks: u32,
}
